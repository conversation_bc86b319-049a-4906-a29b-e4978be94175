[{"type": "text", "text": "产品介绍：超低排放管控治一体化平台和超低排放 ", "text_level": 1, "page_idx": 0}, {"type": "text", "text": "改造服务", "text_level": 1, "page_idx": 0}, {"type": "text", "text": "1 设计思想", "text_level": 1, "page_idx": 0}, {"type": "text", "text": "1.1 设计依据", "text_level": 1, "page_idx": 0}, {"type": "text", "text": "1.1.1 政策文件", "text_level": 1, "page_idx": 0}, {"type": "text", "text": "（1）《中华人民共和国环境保护法》（2）《中华人民共和国大气污染防治法》（3）《2019年全国大气污染防治工作要点》（4）《打赢蓝天保卫战三年行动计划》（国发（2018）22号）（5）《关于印发大气污染防治行动计划的通知》（6）《关于推进实施钢铁行业超低排放的意见》（环大气（2019）35号）（7）《关于做好钢铁企业超低排放评估监测工作的通知》（环大气函（2019）922号）（8）《重污染天气重点行业应急减排措施制定技术指南（2020 年修订版）》（环办大气函[2020]340号）（9）《关于进一步规范重污染天气应急减排措施的函》（环办便函（2021）439号）（10）《钢铁企业超低排放改造技术指南》（中环协4号）（11）《排污许可证申请与核发技术规范钢铁工业》（HJ846-2017）（12）《关于钢铁企业超低排放改造和评估监测公示终止申报或撤销公示的相关规定》（钢协环专（2022）5号）（13）《关于发布《超低排放改造评估监测报告》模板的通知》（钢协环专（2022）18号）", "page_idx": 0}, {"type": "text", "text": "1.1.2 技术标准", "text_level": 1, "page_idx": 0}, {"type": "text", "text": "（1）《环境空气颗粒物（PM10和PM2.5）连续自动监测系统安装和验收技术规范》（HJ655-2013）", "page_idx": 0}, {"type": "text", "text": "（2）《固定污染源烟气（SO2、NOx、颗粒物）排放连续监测系统技术要求及检测方法（HJ76-2017）》", "page_idx": 1}, {"type": "table", "img_path": "images/6d8bcfd361435f14b4ad275eee816699fc13d0937d4d669f2d1f392948865985.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>（3）《污染物在线监控（监测）系统数据传输标准》（HJ212-2017)</td></tr><tr><td></td></tr><tr><td>（4）《安全防范视频监控联网技术要求》（GB/T28181-2011）</td></tr><tr><td>（5）《电子计算机机房设计规范》（GB50174-2008）</td></tr><tr><td></td></tr><tr><td>（6）《计算机软件测试规范》（GB/T15532-2008）</td></tr><tr><td></td></tr><tr><td>（7）《信息技术服务》（GB/T28827）</td></tr><tr><td>（8）《钢铁工业环境保护设计规范》（GB50406-2017）</td></tr><tr><td>（9）《数据中心设计规范》（GB50174-2017）</td></tr><tr><td>（10）《计算机软件需求规格说明规范》（GB/T9385-2008）</td></tr><tr><td></td></tr><tr><td>（11）《外壳防护等级》（GB4208-2017）</td></tr><tr><td>（12）《自动化仪表工程施工及质量验收规范》（GB50093-2013）</td></tr><tr><td></td></tr><tr><td>（13）《电气装置安装工程电缆线路施工及验收规范》（GB50168-2018）</td></tr><tr><td></td></tr><tr><td>（14）《基于Modbus协议的工业自动化网络规范》（GB50168-2018）</td></tr><tr><td>（15）《输送流体用无缝钢管》（GB/T8163-2018）</td></tr><tr><td>（16）《钢制压力容器》（GB150-2018.8）</td></tr><tr><td>（17）《外壳防护等级》（GB4208-2017）</td></tr><tr><td></td></tr><tr><td>（18）《工程机械焊接件通用技术条件》（JB/T5943-2018）</td></tr><tr><td>（19）《电气装置安装工程电缆线路施工及验收规范》（GB50168-2018）</td></tr><tr><td></td></tr><tr><td>（20）《远程射雾技术应用规范》（DB13/T1264-2010）</td></tr><tr><td></td></tr><tr><td>（21）《微米级干雾抑尘技术应用规范》DB13/T1263-2010</td></tr></table>", "page_idx": 1}, {"type": "text", "text": "1.2 设计、建设原则", "text_level": 1, "page_idx": 1}, {"type": "text", "text": "1.2.1 超低排放管控治一体化平台的设计、建设基本原则", "text_level": 1, "page_idx": 1}, {"type": "text", "text": "超低排放管控治一体化平台应具有紧扣实时政策发展迭代更新的能力，并具备核心数据筛选、调阅、查询、回溯和统计分析功能，界面展示需清晰明确，易读易查，界面交互性强。可通过闭环管理模块实行对企业的动态管理，真正实现管、控、治三位一体协助企业进行环保管理，做好超低工作的自证清白，最终达到满足核查要求的目的。", "page_idx": 1}, {"type": "text", "text": "建立全厂无组织排放治理设施集中控制系统，记录所有无组织排放源附近监测、监控和治理设施运行情况以及空气质量监测微站监测数据。", "page_idx": 2}, {"type": "text", "text": "超低排放管控治一体化平台对厂内无组织排放源清单中所有监测、治理设备进行集中管控，并记录各无组织排放源点相关生产设施运行状况、收尘、抑尘、清洗等治理设施运行数据、颗粒物监测数据和视频监控历史数据。", "page_idx": 2}, {"type": "text", "text": "建立企业无组织排放治理设施集中控制系统功能及配置信息，检查是否具备数据异常报警功能、异常情况处理及记录等功能，分析无组织排放源清单所对应的生产作业信号、无组织治理设施运行信号、TSP 监测仪监测数据、高清视频监控录像等接入情况，评估其符合性。", "page_idx": 2}, {"type": "text", "text": "1.2.2 超低排放监测监控体系的设计、建设基本原则", "text_level": 1, "page_idx": 2}, {"type": "text", "text": "根据《意见》、《通知》、《指南》等超低排放政策布设，结合企业客户各分厂实际情况合理优化。", "page_idx": 2}, {"type": "text", "text": "1.2.2.1环境空气质量微站", "text_level": 1, "page_idx": 2}, {"type": "text", "text": "投标单位须根据超低排放政策要求，结合排放源清单点位以及环保管理需求完善全厂环境空气质量微站监测规划和部署。空气质量微站数据全部采用无线/有线专网传输方式上传至超低排放管控治一体化平台，并通过超低排放管控治一体化平台进行数据可视化展示与智能分析。", "page_idx": 2}, {"type": "text", "text": "1.2.2.2排放源粉尘TSP浓度监测体系", "text_level": 1, "page_idx": 2}, {"type": "text", "text": "产尘源监测TSP须进行所有无组织排放点位的全覆盖布设，且能够为企业客户现场无组织排放管控情况自证守法，采用无线/有线专网传输方式将实时监测数据上传至超低排放管控治一体化平台数据中心。", "page_idx": 2}, {"type": "text", "text": "（1）物料存储区域监测体系部署原则", "text_level": 1, "page_idx": 2}, {"type": "text", "text": "$\\textcircled{1}$ 对物料存储区域内的所有可能的产尘点，均应实现有效的粉尘监测：根据粉尘扩散的特点，应对可能产尘点上方5-10m空间形成监测全覆盖，不应有监测盲区；", "page_idx": 2}, {"type": "text", "text": "$\\textcircled{2}$ 粉尘监测设备的选型需满足区域污染特点，适应产尘源扬尘特点，应达到精准、快速的监测效果；", "page_idx": 2}, {"type": "text", "text": "$\\textcircled{3}$ 料场内监测部署方案，应对料场精准治理提供指导建议。", "page_idx": 2}, {"type": "text", "text": "（2）物料输送及生产工艺环节监测体系部署原则", "text_level": 1, "page_idx": 2}, {"type": "text", "text": "$\\textcircled{1}$ 排放源清单中所有污染点均应达到有效覆盖，不应有显著的监测盲区；$\\textcircled{2}$ 各生产工艺线的各个生产工艺环节，均应有针对性地部署监测设备，监  \n测设备选型应与相应环节的污染特点相匹配；$\\textcircled{3}$ 排放源密集且相互连通的高风险污染区域，如高炉矿槽、烧结配料间、  \n高位料仓等，应对各排放源形成 $100 \\%$ 针对性自证监测，监测点距排放源点不", "page_idx": 3}, {"type": "text", "text": "应超过2m距离；", "text_level": 1, "page_idx": 3}, {"type": "text", "text": "$\\textcircled{4}$ 重点核查区域，且为治理薄弱环节，应实现 $100 \\%$ 针对性自证监测；$\\textcircled{5}$ 其他重点核查区域，监测设备部署应具备代表性，应实现 $100 \\%$ 覆盖率；$\\textcircled{6}$ 其他风险区域，监测设备部署应综合考虑污染时间、污染风险、工艺流", "page_idx": 3}, {"type": "text", "text": "程、污染关系等因素，不应出现污染特点表征不足的情况；", "page_idx": 3}, {"type": "text", "text": "$\\textcircled{7}$ 对于有防爆需求的设备部署位置，需安装防爆型设备；", "page_idx": 3}, {"type": "text", "text": "1.2.2.3重点污染区域高清视频监控", "text_level": 1, "page_idx": 3}, {"type": "text", "text": "根据超低排放政策要求，料场出入口、烧结环冷区域、高炉矿槽和炉顶区域、炼钢车间顶部等易产尘点，须安装高清视频监控设施，监控区域及视角满足超低排放评估要求。视频监控数据至少要保存六个月以上。具体政策要求：", "page_idx": 3}, {"type": "text", "text": "《关于推进实施钢铁行业超低排放的意见（环大气（2019）35号）》：料场出入口、烧结环冷区域、高炉矿槽和炉顶区域、炼钢车间顶部等易产尘点，应安装高清视频监控设施。视频监控数据至少要保存六个月以上。《钢铁企业超低排放评估监测技术指南（环办大气函（2019）922号）》：企业门禁和视频监控系统具备保存六个月以上数据能力。《钢铁企业超低排放改造技术指南（中环协（2020）4号)》：料场出入口、烧结环冷区域、高炉矿槽和炉顶区域、炼钢车间顶部、焦炉炉顶、钢渣处理车间等易产尘点安装高清视频监控装置。", "page_idx": 3}, {"type": "text", "text": "1.2.2.4清洁环保车辆定位", "text_level": 1, "page_idx": 3}, {"type": "text", "text": "根据超低政策要求，易玖生态为企业客户所有环保清洁车辆加装北斗定位系统，记录环保清洁车辆位置轨迹和历史工作情况。", "page_idx": 3}, {"type": "text", "text": "1.2.3 数据采集、接入及管理的设计、建设基本原则", "text_level": 1, "page_idx": 3}, {"type": "text", "text": "项目涉及的无组织，有组织生产、治理、监测数据，门禁数据的采集及接入均须在超低排放管控治一体化平台进行可视化展示。无组织数据通过现场", "page_idx": 3}, {"type": "text", "text": "PLC、DCS或设备直接采集运行数据推送至平台；有组织数据通过 DCS 授权OPC 协议方式进行采集至平台；门禁系统数据通过有线方式直接推送至平台。", "page_idx": 4}, {"type": "text", "text": "1.2.4 清洁运输门禁管理系统设计、建设基本原则", "text_level": 1, "page_idx": 4}, {"type": "text", "text": "依据《意见》、《通知》和《重点行业移动源监管与核查技术指南》（HJ1321-2023）等政策文件要求，企业须在厂区货运门建设门禁系统和视频监控系统，监控运输车辆进出厂区情况。", "page_idx": 4}, {"type": "text", "text": "近三个月进出企业的铁精矿、煤炭、焦炭、废钢，以及外购烧结矿、外购球团矿、石灰等大宗物料和钢渣、水渣、钢材（含外卖中间产品）等产品采用铁路、水路、管道或管状带式输送机等清洁方式运输比例达到 $80 \\%$ （含）以上，或清洁方式运输比例达不到 $80 \\%$ ，进出厂公路运输车辆全部采用新能源汽车或达到国六排放标准。", "page_idx": 4}, {"type": "text", "text": "建立进出厂大宗物料和产品运输基础数据电子台账，其中，铁路运输应有磅单记录电子台账，水路运输应有水尺记录电子台账，管状带式输送运输应有皮带秤记录电子台账，管道输送应有磅单记录台账或皮带秤记录电子台账。企业门禁和视频监控系统应监控并记录进出厂运输车辆的完整车牌号、车辆排放阶段。", "page_idx": 4}, {"type": "text", "text": "厂内运输车辆和非道路移动机械应完成编码登记。", "page_idx": 4}, {"type": "text", "text": "1.2.5 无组织移动源治理改造方案的设计、建设基本原则", "text_level": 1, "page_idx": 4}, {"type": "text", "text": "（1）料场内部治理设施配备情况评估", "text_level": 1, "page_idx": 4}, {"type": "text", "text": "逐一说明料场内部雾炮、干雾抑尘、除尘等措施配置情况，包括数量、参数、启动方式等，以视觉化数据结果评估治理设施的有效性及料场内现场环境情况。附各治理设施、监测监控设施配置图，及雾炮、干雾抑尘、除尘器现场照片。", "page_idx": 4}, {"type": "text", "text": "（2）无组织控制设施运行情况及有效性评估", "text_level": 1, "page_idx": 4}, {"type": "text", "text": "能够调取无组织治理设施集中控制系统中各料场1个月的运行历史数据，通过生产作业信号和治理设施曲线，逐一评估料场内雾炮、干雾抑尘、除尘等治理设施运行情况，包括污染行为发生情况的数据、设备启停次数、时间、治理设施与生产设备同步运行情况；再结合TSP监测数据综合评估治理效果，重点评估TSP 监测数据出现高频、高幅波动的时间段。附历史曲线截图、料场内", "page_idx": 4}, {"type": "text", "text": "装卸料作业高清视频。", "page_idx": 5}, {"type": "text", "text": "1.3 设计思路", "text_level": 1, "page_idx": 5}, {"type": "text", "text": "“超低排放管控治一体化平台\"是利用云计算、物联网通信技术及大数据统计与分析等主流技术搭建。通过全天候在线监控企业污染物排放情况及污染处理设施运行情况，构建全方位、多层次、全覆盖的企业环境监测网络，响应环保要求，实现企业各类污染物的污染预防、达标排放，提高环境管理工作效率，对监测数据进行深度挖掘与应用，以更精细的动态方式实现企业环境管理和决策的智慧化。", "page_idx": 5}, {"type": "text", "text": "1.3.1对外功能：明确政策标准、建立功能体系、直观体现监管内容；", "text_level": 1, "page_idx": 5}, {"type": "text", "text": "通过梳理政策文件的要求，对平台功能模块进行系统规划，使超低排放和绩效等级评价过程中，直观快速地将等级划分要求内容呈现，使督查组、参观团人员在智慧环保综合管控平台就能快速了解企业情况，展现企业的环保建设水平：", "page_idx": 5}, {"type": "text", "text": "1.3.2对内功能：治理是根本、监管是过程、管理是关键；", "text_level": 1, "page_idx": 5}, {"type": "text", "text": "源头治理是根本，监测监督是过程，分析监测结果，提供管理建议、助力管理提升才是平台的关键价值。我们将从产尘源头入手分析，展示监测结果，运用大数据分析技术，为各管理层实时把握厂内污染排放情况和环境管控运行情况提供信息化工具，结合企业客户的需求，平台加入日常管理等板块，提升环保部的工作效能。平台（PC端和移动端）将定位为环保专业管理应用，伴随企业的环保管理提升。", "page_idx": 5}, {"type": "text", "text": "1.3.3重点环节：设计先行、合理分工、最大程度避免改造；", "text_level": 1, "page_idx": 5}, {"type": "text", "text": "企业客户现正处于项目建设时期，易玖生态根据长期的项目经验，对平台建设中数据采集的许多内容，在设计中提出较为适用的、适配性强的方案和思路，以便企业客户建设过程中直接采用，最大程度减少建成后改造工作量。", "page_idx": 5}, {"type": "text", "text": "网络规划方面，超低排放管控治一体化平台计划构建在一个独立的网络段内，通过核心交换机与其他网络段做了隔离，同企业办公网络、生产网络互不干涉，建成后可以通过配置交换机路由、防火墙设置将需要接入环保网络的电脑同平台连通，使环保部、分厂等需要使用环保平台的相关人员登录环保平台使用。", "page_idx": 5}, {"type": "text", "text": "2 超低排放管控治一体化平台建设", "text_level": 1, "page_idx": 6}, {"type": "text", "text": "超低排放管控治一体化平台的设计本着“简捷、安全、实用、可靠\"的原则，采用目前国际领先的通讯技术方式，平台能及时掌握和了解工艺流程中设备的运行工况、工艺参数的变化，有效优化工艺流程，保证工艺稳定、安全运行，并降低运行成本，提高管理效率，增加长期运行的稳定性。", "page_idx": 6}, {"type": "text", "text": "2.1 软件系统建设", "text_level": 1, "page_idx": 6}, {"type": "text", "text": "超低排放管控治一体化平台是通过物联网、大数据等技术，将全厂的有组织、无组织排放源、排放数据及环保、生产设施运行进行集中管控，为日常环保设施正常运行及无组织达标排放提供保障，同时满足环保管理部门对企业超低排放运行的监督检查要求，自证清白。是企业展示环保管理能力建设的窗口。", "page_idx": 6}, {"type": "text", "text": "平台由易玖生态自主研发，已有相对应的软著申请资料。", "page_idx": 6}, {"type": "text", "text": "平台系统使用独立服务器部署，用户端支持PC、手机等设备，同时可以快速部署。根据管理需要，可以分类设置用户权限，实现终端信息查询、浏览、操作等业务，同时支持后期功能扩展。", "page_idx": 6}, {"type": "text", "text": "易玖生态已经通过CMMI3软件成熟度认证，且具有多个项目的成功实施经验。分别推出了针对钢铁、焦化、水泥行业的通用基础版软件平台，并在基础版软件平台上同客户一起共创，结合企业的需求，打造符合企业自身平台软件系统。", "page_idx": 6}, {"type": "text", "text": "2.1.1系统架构", "text_level": 1, "page_idx": 6}, {"type": "text", "text": "环境综合管控平台采用微服务架构，系统架构图如下：", "text_level": 1, "page_idx": 6}, {"type": "image", "img_path": "images/d698fb9bb6807e4432c34e4edabcc443ac478c46b6511efa990d2e5cb5c80ca8.jpg", "image_caption": ["环境综合管控平台系统构架图"], "image_footnote": [], "page_idx": 6}, {"type": "text", "text": "整个平台系统从数据源到数据传输，再到数据采集、数据存储，然后经过平台底座最以功能应用的形式呈现，整个软件系统采用微服务、插件式持续集成的架构设计方案，是一种不断演进的高可用、高性能、可伸缩和低成本的技术架构体系。", "page_idx": 7}, {"type": "text", "text": "2.1.2基础支撑体系", "text_level": 1, "page_idx": 7}, {"type": "text", "text": "基础支撑层采用虚拟化与容器技术，构建动态、弹性的服务器集群基础支撑环境。资源监控系统对后端服务进行实时监控，并根据各服务的负载情况进行动态调配，确保各个服务在保有适当冗余的情况下，提供最大限度的负载支撑，从而在基础支撑实现高性能、可伸缩的技术架构体系。", "page_idx": 7}, {"type": "text", "text": "2.1.3数据存储", "text_level": 1, "page_idx": 7}, {"type": "text", "text": "由于在设计阶段就考虑到了海量数据存储的需求，并且考虑到环保数据重要性，故加入了数据多级备份的设计。按天、月、季年进行数据备份。此外还可通过增加服务器硬盘的方式扩容存储空间，扩容后理论可达存储10年以上数据。结合消息队列异步数据处理机制、内存数据库数据缓存层等多种优化方法，在数据库层面确保高可用与高性能。", "page_idx": 7}, {"type": "text", "text": "2.1.4微服务架构", "text_level": 1, "page_idx": 7}, {"type": "text", "text": "采用微服务架构设计，确保每一个服务都对应唯一的业务能力，做到单一职责，服务间互相独立，互不干扰，各服务提供统一的接口调用规则，服务间虽然有调用，但做到了服务重启不影响其它服务。每个服务都是独立的组件，可复用，可替换，低耦合，易维护，有利于持续集成。", "page_idx": 7}, {"type": "text", "text": "2.1.5API数据接口", "text_level": 1, "page_idx": 7}, {"type": "text", "text": "采用统一的API数据接口层进行数据交互，在API数据接口层做统一的安全校验，鉴权操作，将前端访问与后端服务做安全隔离，确保API数据接口层的高性能与高可用性。", "page_idx": 7}, {"type": "text", "text": "2.1.6产品优势", "text_level": 1, "page_idx": 7}, {"type": "text", "text": "(1) 丰富的业绩案例和行业实战经验 ", "text_level": 1, "page_idx": 7}, {"type": "text", "text": "易玖生态有丰富的钢铁、焦化、水泥行业超低排放平台案例经验，有成熟的产品，具备承担超低排放项目总承包的能力。有自己的咨询、工程设计、软件开发、售后运营团队，实战经验丰富。", "page_idx": 8}, {"type": "text", "text": "易玖生态在环保领域拥有丰富经验，懂得环保在发展过程的侧重点，而且近年来在大数据和软件领域进行拓展，是国内无组织排放集中控制系统平台最早参与和研发的团队之一，行业经验丰富，拥有较多平台业绩，能够提供客户验收证明和运行业绩供考察。", "page_idx": 8}, {"type": "text", "text": "(2) 具备自主开发能力，保障软件产品的先进性和拓展性", "text_level": 1, "page_idx": 8}, {"type": "text", "text": "易玖生态有自己的软件团队，软件平台具有自主软件著作权，可以根据企业要求，定制开发想要的功能，具有很好的先进性和拓展性。", "page_idx": 8}, {"type": "text", "text": "易玖生态软件产品均为自研，具有自主知识产权，拥有多项钢铁、焦化、建材行业无组织排放监控系统软件产品著作权证书，设计理念先进，满足政策要求，行业领先。", "page_idx": 8}, {"type": "text", "text": "软件监测数据本地化部署（物理服务器或私有云），数据安全，所有运行中产生的数据所有权归企业客户，企业客户可利用数据进行数据挖掘、深度学习等。", "page_idx": 8}, {"type": "text", "text": "软件系统扩展性强，系统可作为承载系统或数据总线对接有组织板块、分表计电板块、物流板块、能源板块、安全板块以及生产系统板块等。", "page_idx": 8}, {"type": "text", "text": "移动端和PC端针对不同级别人员、不同需求设置不同应用，功能设计实用方便，满足高层“一张图掌握全貌”、中层“预警告警管理提效”、基层“使用便捷数据可靠\"等实际需求。", "page_idx": 8}, {"type": "text", "text": "易玖生态目前拥有《软件能力成熟度模型集成（CMMI3级）证书》和近40项计算机软件著作权登记证书：其中27项与钢铁、焦化、水泥行业监管平台系统相关，其余项包含有智能雾炮系统、有组织CEMS数据监测、颗粒物扬尘数据监测采集传输终端软件等。", "page_idx": 8}, {"type": "text", "text": "主要软著权包括：", "page_idx": 8}, {"type": "text", "text": "软件名称：钢铁行业超低排放管控治一体化平台无组织排放监管系统V1.0软件著作权登记证书号：软著登字第8131057号", "page_idx": 8}, {"type": "text", "text": "2） 软件名称：钢铁行业超低排放管控治一体化平台有组织排放监管系统V1.0  \n软件著作权登记证书号：软著登字第8130562号  \n3）软件名称：焦化行业超低排放管控拍设备管理系统V1.0  \n软件著作权登记证书号：软著登字第8410244号  \n4）软件名称：易玖生态焦化行业环境管理平台无组织排放在线监测系统V3.0  \n软件著作权登记证书号：软著登字第6093828号  \n5） 软件名称：易玖生态钢铁行业环境管理平台无组织排放在线监测系统V3.0  \n软件著作权登记证书号：软著登字第6094139号  \n6）软件名称：水泥行业低碳环保管控治一体化平台V1.0  \n软件著作权登记证书号：软著登字第8712448号  \n7）软件名称：水泥行业超低排放无组织监管平台V1.0  \n软件著作权登记证书号：软著登字第10191162号  \n8）软件名称：易玖生态建材行业环境管理平台无组织排放在线监测系统V1.0  \n软件著作权登记证书号：软著登字第3068601号  \n9） 软件名称：易玖生态电力数据采集传输终端软件V1.0  \n软件著作权登记证书号：软著登字第4460446号  \n10） 软件名称：有组织CEMS监测数据采集传输终端软件V1.0  \n软件著作权登记证书号：软著登字第8720033号  \n11） 软件名称：空气质量监测微站数据采集传输终端软件V1.0  \n软件著作权登记证书号：软著登字第9389053号  \n12) 软件名称：颗粒物扬尘数据采集传输终端软件V1.0  \n软件著作权登记证书号：软著登字第9695966号  \n13) 软件名称：钢铁行业除尘器治理设施运行数据采集传输终端软件V1.0  \n软件著作权登记证书号：软著登字第10269772号", "page_idx": 9}, {"type": "text", "text": "14） 软件名称：钢铁行业超低排放管控治一体化平台环境质量管理系统V1.0", "page_idx": 10}, {"type": "text", "text": "软件著作权登记证书号：软著登字第8129844号", "page_idx": 10}, {"type": "text", "text": "15) 软件名称：钢铁行业超低排放管控治一体化平台清洁运输监管系统V1.0  \n软件著作权登记证书号：软著登字第8130949号", "page_idx": 10}, {"type": "text", "text": "16） 软件名称：钢铁行业超低排放管控治一体化平台设备管理系统V1.0软件著作权登记证书号：软著登字第8130943号", "page_idx": 10}, {"type": "text", "text": "(3） 具备丰富的超低排放验收经验 ", "page_idx": 10}, {"type": "text", "text": "易玖生态具备验收专家视野和经验，与参与超低评审的单位保持良好的沟通，及时掌握生态环境部、协会的政策动态及发展趋势。产品历经行业部委验收专家组以及河北、山西、山东、河南各省级验收组现场指导，可以帮助企业一次通过验收。", "page_idx": 10}, {"type": "text", "text": "(4) 工程设计经验丰富", "text_level": 1, "page_idx": 10}, {"type": "text", "text": "可根据企业实际情况进行多方位的优化，帮助企业节省投入资金；丰富的部署、数据采集与信号接入经验：有效避免了客户现场许多设备和平台的对接问题，确保项目按期完工投用。可以根据企业的时间节点，在保障质量的前提下尽可能的缩短部署时间。", "page_idx": 10}, {"type": "text", "text": "(5) 可为企业提供陪伴式服务", "text_level": 1, "page_idx": 10}, {"type": "text", "text": "易玖生态对超低排放改造有过长时间驻场服务经验，经常全过程伴随企业，具备丰富的现场迎检经验，助力企业通过超低排放验收。", "page_idx": 10}, {"type": "text", "text": "2.2 平台建设原则", "text_level": 1, "page_idx": 10}, {"type": "text", "text": "超低排放管控治一体化平台建设遵从以下原则：", "page_idx": 10}, {"type": "text", "text": "1.统一性原则 ", "text_level": 1, "page_idx": 10}, {"type": "text", "text": "以软件平台系统为中心，在拓展应用时，数据统一从服务平台中获取。各应用子系统遵守统一的数据源、统一的数据模型和统一的数据接口原则。", "page_idx": 10}, {"type": "text", "text": "2.开放性原则 ", "text_level": 1, "page_idx": 10}, {"type": "text", "text": "系统中的各种网络协议、硬件接口和数据接口等符合开放式标准。通过数据封装、API等方式开放系统数据和应用，全面支持与其它系统的数据对接和应用需求，提升平台应用的广度和深度。", "page_idx": 11}, {"type": "text", "text": "3.实用性原则 ", "text_level": 1, "page_idx": 11}, {"type": "text", "text": "系统应用建设满足使用人员业务需求，能够解决不同层次使用人员的实际问题。应用开发设计符合使用人员的工作场景，能够对其实际工作进行指导，提高其工作效率。", "page_idx": 11}, {"type": "text", "text": "4.易用性原则 ", "text_level": 1, "page_idx": 11}, {"type": "text", "text": "系统应实现用户可接受的查询效率与响应时间，支持PC、手机等多种终端，有良好易用的人机接口界面与灵活多样的展现方式。", "page_idx": 11}, {"type": "text", "text": "5.安全与保密性原则", "text_level": 1, "page_idx": 11}, {"type": "text", "text": "平台系统必须保证信息的安全，有较好的数据安全措施，有较好的安全控制措施，有较强的数据恢复能力。加强对要充分考虑数据的保密措施，各种数据必须受到严格控制，防止非正常渠道的提取、修改。", "page_idx": 11}, {"type": "text", "text": "6.可行性原则 ", "text_level": 1, "page_idx": 11}, {"type": "text", "text": "要以满足提供实时的优质的可视化的数据应用服务，要保证技术的可靠性和经济的可行性。计算机系统、通讯网络系统、数据设计，在技术上必须是成熟的，实践检验是成功的，经济上是可实现的技术手段。", "page_idx": 11}, {"type": "text", "text": "7.适应性原则 ", "text_level": 1, "page_idx": 11}, {"type": "text", "text": "方案要保证可扩充性、可维护性：方案中涉及的软硬件都留有扩充升级的接口；方案的落地要便于维护，对企业的需求发展有较强的适应能力。", "page_idx": 11}, {"type": "text", "text": "8.先进性原则 ", "text_level": 1, "page_idx": 11}, {"type": "text", "text": "在实用可靠的前提下，尽可能跟踪国内外先进的计算机软硬件技术、信息技术及网络通信技术，使系统具有较高的性能价格比。采用先进的体系结构和技术发展的主流产品，保证整个系统高效运行。", "page_idx": 11}, {"type": "text", "text": "9.可靠性原则 ", "text_level": 1, "page_idx": 11}, {"type": "text", "text": "方案的设计需保证使用成熟的尽量商品化的技术、产品，以及公开开放的协议，保证所使用的技术是经过实践考验过，技术先进的。", "page_idx": 11}, {"type": "text", "text": "10.可维护性原则 ", "text_level": 1, "page_idx": 11}, {"type": "text", "text": "方案考虑如何监控系统的运行状态，在发现问题的时候，能够做出一些简单、合适操作，保证整个系统长期、稳定的维护。", "page_idx": 12}, {"type": "text", "text": "2.3 平台功能清单", "text_level": 1, "page_idx": 12}, {"type": "text", "text": "易玖生态负责提供包含大屏端、PC端、移动端（支持IOS端、Android端和 HarmonyOS端（按需））的超低排放管控治一体化平台。通过集中控制系统对全厂无组织排放源进行集中管理，并接入相关联的生产、治理、监测设备信号进行同步性的展示，以满足无组织超低排放的核查要求以及现场管理需要。", "page_idx": 12}, {"type": "text", "text": "具体功能如下：", "page_idx": 12}, {"type": "text", "text": "2.3.1 大屏端 ", "text_level": 1, "page_idx": 12}, {"type": "text", "text": "2.3.1.1 首页", "text_level": 1, "page_idx": 12}, {"type": "text", "text": "2.3.1.1.1 数据看板：", "page_idx": 12}, {"type": "text", "text": "一级菜单：首页；", "page_idx": 12}, {"type": "text", "text": "二级菜单：数据看板；", "page_idx": 12}, {"type": "text", "text": "功能描述：", "page_idx": 12}, {"type": "text", "text": "首页提供可视化看板，集中展示关键信息，如有组织/无组织排放监测数据、清洁运输执行情况统计、企业宣传内容等。用户可根据自身需求，拖拽式布局编辑展示组件，灵活调整看板布局和展示的数据模块。", "page_idx": 12}, {"type": "text", "text": "企业多媒体展示：支持上传介绍企业的视频或图片，并在看板区域以视频或图文形式展示。", "page_idx": 12}, {"type": "text", "text": "2.3.1.1.2 地图展示与控制：", "text_level": 1, "page_idx": 12}, {"type": "text", "text": "一级菜单：首页；", "page_idx": 12}, {"type": "text", "text": "二级菜单：地图展示与控制；", "page_idx": 12}, {"type": "text", "text": "功能描述：", "page_idx": 12}, {"type": "text", "text": "厂区地图展示与控制：集成高精度BIM（建筑信息模型）与GIS（地理信息系统)，构建并展示厂区二/三维实景地图。支持地图的自由缩放、平移和旋转等操作。地图上叠加显示关键点位（POI）信息，如监测设备位置及其当前状态。提供视角调整功能，并可一键恢复至预设的初始地图视角。", "page_idx": 12}, {"type": "text", "text": "监测因子数据空间分布：允许用户在地图上按特定监测因子（如PM2.5、SO2等）筛选和查询相关设备及其监测数据。默认展示所有因子数据，用户可根据需要选择显示或隐藏特定因子的信息。", "page_idx": 13}, {"type": "text", "text": "环境质量热力图分析：基于环境质量监测设备的实时或历史数据，在地图上生成热力图，直观展示污染物浓度或其他环境指标的空间分布情况。支持按小时数据动态播放热力图变化。用户可通过日期选择器查询指定某天的热力图数据（查询粒度为天）。", "page_idx": 13}, {"type": "text", "text": "2.3.1.1.3 地图设备筛选与查看：", "text_level": 1, "page_idx": 13}, {"type": "text", "text": "一级菜单：首页；", "page_idx": 13}, {"type": "text", "text": "二级菜单：地图设备筛选与查看；", "page_idx": 13}, {"type": "text", "text": "功能描述：", "page_idx": 13}, {"type": "text", "text": "地图设备类型筛选与信息查询：在地图上清晰标注各类设备的位置，包括生产、污染治理、监测、视频监控以及环境质量监测设备等。提供按设备类型进行筛选的功能，方便用户快速查看各类设备分布位置，并查看其详细信息和关联数据。", "page_idx": 13}, {"type": "text", "text": "2.3.1.1.4 界面布局调整：", "text_level": 1, "page_idx": 13}, {"type": "text", "text": "一级菜单：首页；二级菜单：界面布局调整；功能描述：", "page_idx": 13}, {"type": "text", "text": "看板折叠/展开：提供看板区域的折叠功能。用户可点击“收起\"按钮将看板完全隐藏，以获得全屏的地图操作区域；再次点击可重新展开看板。", "page_idx": 13}, {"type": "text", "text": "2.3.1.1.5 平台个性化配置：", "text_level": 1, "page_idx": 13}, {"type": "text", "text": "一级菜单：首页；  \n二级菜单：平台个性化配置；  \n功能描述：", "page_idx": 13}, {"type": "text", "text": "界面主题配色切换：允许用户根据偏好或特定场景需求，切换整个平台的界面主题颜色。当前提供蓝色、绿色、紫色等多种预设主题方案。", "page_idx": 13}, {"type": "text", "text": "模式切换：支持平台在不同运行模式间切换：", "page_idx": 13}, {"type": "text", "text": "自定义模式：标准操作模式，用户拥有完全权限，可根据需要配置平台功能、显示字段等。", "page_idx": 14}, {"type": "text", "text": "迎检模式：特殊模式，通常用于对外展示或接受检查。在此模式下，所有涉及配置、修改的操作按钮将被隐藏，呈现一个相对固定的信息展示界面。", "page_idx": 14}, {"type": "text", "text": "2.3.1.2无组织监管", "text_level": 1, "page_idx": 14}, {"type": "text", "text": "2.3.1.2.1 污染源清单：", "text_level": 1, "page_idx": 14}, {"type": "text", "text": "一级菜单：无组织监管；", "page_idx": 14}, {"type": "text", "text": "二级菜单：污染源清单；", "page_idx": 14}, {"type": "text", "text": "功能描述：", "page_idx": 14}, {"type": "text", "text": "污染源清单：提供无组织排放源的清单化管理。实时采集并展示各排放源关联的生产设备、治理设施的运行状态信号，并与该源对应的TSP（总悬浮颗粒物）浓度监测数据在同一界面进行对比，便于用户分析排放与生产、治理活动之间的关联性。支持按排放源名称、所属单元、工序、编号以及关联的生产/治理设备编号或名称进行快速筛选和查询。在自定义模式下，用户可灵活调整列表的表头字段顺序、设置字段的显示或隐藏，并将关键数据行置顶显示。", "page_idx": 14}, {"type": "text", "text": "污染源详情：点击清单中的条目可查看排放源的详细信息，包括： $\\textcircled{1}$ 基础信息（如排放源名称、排放物料种类、采取的治理措施等）； $\\textcircled{2}$ 动态监测数据（关联生产/治理设施的启停记录、关键运行参数、TSP浓度历史曲线对比等，支持历史数据查询与回溯)。同时，在地图上联动显示该排放源的地理位置、基础信息和实时运行状态。用户可在详情页面自定义显示或隐藏特定设备或监测因子（参数）的数据曲线。", "page_idx": 14}, {"type": "text", "text": "2.3.1.2.2 污染源监测清单：", "text_level": 1, "page_idx": 14}, {"type": "text", "text": "一级菜单：无组织监管；二级菜单：污染源监测清单；功能描述：", "page_idx": 14}, {"type": "text", "text": "TSP 监测清单：集中展示全厂范围内主要产尘点安装的TSP监测设备的实时数据。提供独立的查询入口，用户可查看历史数据曲线，按当前监测数值进行升序或降序排列。支持自定义配置列表的显示字段（选择显示/隐藏哪些数据列）。", "page_idx": 14}, {"type": "text", "text": "TSP高频高幅分析：对TSP监测数据进行高频高幅（指短时间内数值剧烈波动或出现异常峰值）分析，用于识别潜在的异常排放事件。用户可按分厂、设备编号/名称、时间段查询此类异常事件的发生记录。当系统检测到高频高幅数据时，能自动触发报警。", "page_idx": 15}, {"type": "text", "text": "TSP 多设备同曲线对比：支持选择多个TSP监测设备，在同一图表上对比展示它们在选定时段内的历史数据变化曲线。进行多设备对比时，每次只能选择 TSP 浓度这一个因子进行比较。", "page_idx": 15}, {"type": "text", "text": "2.3.1.2.3 环卫车监管：", "text_level": 1, "page_idx": 15}, {"type": "text", "text": "级菜单：无组织监管；二级菜单：环卫车监管；功能描述：", "page_idx": 15}, {"type": "text", "text": "环卫车监管：记录并统计环卫车（如洒水降尘车）的运行时间、作业时长，并实时追踪其GPS轨迹，支持历史轨迹回放。当厂界道路附近的微型监测站监测到空气质量指标超标时，系统可自动向在附近作业的环卫车司机发送报警提醒，以便及时响应并加强降尘作业。", "page_idx": 15}, {"type": "text", "text": "2.3.1.2.4 洗车台管理：", "text_level": 1, "page_idx": 15}, {"type": "text", "text": "级菜单：无组织监管；二级菜单：洗车台管理；功能描述：", "page_idx": 15}, {"type": "text", "text": "洗车记录清单：详细记录进出厂车辆在洗车台的清洗信息，包括车牌号、开始/结束清洗时间、冲洗水压、现场抓拍照片等。用户可以切换查看不同洗车台的记录，按时间段查询，并查看各洗车台的使用统计数据。支持自定义配置记录列表的显示字段", "page_idx": 15}, {"type": "text", "text": "未洗车记录清单：对于系统抓拍到的疑似未按规定进行清洗的车辆记录，提供查询功能。用户可按不同洗车台和时间段筛选查看这些未洗车事件的抓拍照片。", "page_idx": 15}, {"type": "text", "text": "2.3.1.2.5 物料储存智能治理：", "text_level": 1, "page_idx": 15}, {"type": "text", "text": "一级菜单：无组织监管；二级菜单：物料储存智能治理；", "page_idx": 15}, {"type": "text", "text": "功能描述：", "text_level": 1, "page_idx": 16}, {"type": "text", "text": "智能雾炮：料场（如料棚）部署的智能雾炮抑尘系统。用户可按料场区域查看雾炮设备列表及状态。系统支持多种智能运行模式：AI视觉联动（AI识别自动启动）、颗粒物浓度联动（监测超标自动启动）、按预设工作计划联动，并支持远程手动启停控制。提供按时间查询雾炮的详细运行记录和联动触发记录。", "page_idx": 16}, {"type": "text", "text": "2.3.1.2.6 除尘器管理：", "text_level": 1, "page_idx": 16}, {"type": "text", "text": "一级菜单：无组织监管；二级菜单：除尘器管理；功能描述：", "page_idx": 16}, {"type": "text", "text": "除尘器清单：提供全厂除尘设备的清单及状态概览。用户可通过区域（分", "page_idx": 16}, {"type": "text", "text": "厂）、设备名称或编号快速定位查询。以卡片形式直观展示每台除尘器的核心运行参数（如压差、风量、温度等）的实时数据。支持查看选定设备的历史运行数据，并可以折线图或数据列表两种形式展示。", "page_idx": 16}, {"type": "text", "text": "2.3.1.2.7 环境质量监测清单：", "text_level": 1, "page_idx": 16}, {"type": "text", "text": "一级菜单：无组织监管；二级菜单：环境质量监测清单；功能描述：", "page_idx": 16}, {"type": "text", "text": "环境质量监测清单：列表展示厂区内所有微站的静态信息（位置、编号）和实时监测数据（如PM2.5、PM10、噪声等）。支持按实时监测数据进行升序或降序排列，方便快速识别高值或低值点。支持查看单个或多个微站的历史数据曲线，并能在同一图表进行对比分析。用户可自定义配置列表的显示字段。", "page_idx": 16}, {"type": "text", "text": "2.3.1.2.8 环境质量监测：", "text_level": 1, "page_idx": 16}, {"type": "text", "text": "一级菜单：无组织监管；二级菜单：环境质量监测；功能描述：", "page_idx": 16}, {"type": "text", "text": "环境质量监测：提供厂区环境质量监测网络的整体概况。统计展示不同类型监测设备的数量。支持查看微站监测指标的小时平均值、日平均值等。", "page_idx": 16}, {"type": "text", "text": "2.3.1.2.9 历史与国省控站对比：", "text_level": 1, "page_idx": 17}, {"type": "text", "text": "一级菜单：无组织监管；", "page_idx": 17}, {"type": "text", "text": "二级菜单：历史与国省控站对比；", "page_idx": 17}, {"type": "text", "text": "功能描述：", "page_idx": 17}, {"type": "text", "text": "历史与国/省控站数据对比：平台支持展示与厂区所在城市的国/省控站的数据，每小时更新一次。", "page_idx": 17}, {"type": "text", "text": "2.3.1.3有组织监管", "text_level": 1, "page_idx": 17}, {"type": "text", "text": "2.3.1.3.1 排放口清单：", "page_idx": 17}, {"type": "text", "text": "一级菜单：有组织监管；", "page_idx": 17}, {"type": "text", "text": "二级菜单：排放口清单；", "page_idx": 17}, {"type": "text", "text": "功能描述：", "page_idx": 17}, {"type": "text", "text": "排放口清单：提供全厂有组织排放口（包括安装CEMS的重点排口和一般排口）的清单化管理。通过接入关联生产设备、治理设备的DCS关键运行信号，在排放口的详情页面，将这些信号与排放监测数据在同一时间坐标轴上集成展示。提供“生产 治理 排放监测\"全流程数据的可视化，有助于实现对排放口从源头产生到末端排放的全过程动态监管与关联分析。用户可按工序、排放口名称等条件快速筛选或切换查看不同排放口的信息。支持自定义显示/隐藏数据列，并允许用户自定义排放口的排序。对于设定的一般排放口，支持配置并关联查阅其对应的监测报告（如手工比对监测报告）。", "page_idx": 17}, {"type": "text", "text": "2.3.1.3.2 CEMS监测清单：", "text_level": 1, "page_idx": 17}, {"type": "text", "text": "级菜单：有组织监管；二级菜单：CEMS监测清单；功能描述：", "page_idx": 17}, {"type": "text", "text": "CEMS监测清单：以列表形式集中展示所有安装了CEMS的排放口的实时监测数据。用户可以筛选时间范围，查询并以曲线图形式查看分钟、小时、日等不同时间粒度的历史数据变化趋势。支持选择多个设备在同一图表上对比分析它们的历史排放数据曲线，便于横向比较。", "page_idx": 17}, {"type": "text", "text": "2.3.1.3.3 CEMS监测预警：", "text_level": 1, "page_idx": 17}, {"type": "text", "text": "一级菜单：有组织监管；", "page_idx": 17}, {"type": "text", "text": "二级菜单：CEMS监测预警；", "page_idx": 18}, {"type": "text", "text": "功能描述：", "page_idx": 18}, {"type": "text", "text": "CEMS监测预警：支持CEMS设备根据因子展示列表，每5分钟采集并展示一条数据，实时计算当前小时剩余排放浓度，预警当前小时是否有超标风险。", "page_idx": 18}, {"type": "text", "text": "2.3.1.3.4 CEMS监测预警：", "text_level": 1, "page_idx": 18}, {"type": "text", "text": "一级菜单：有组织监管；", "page_idx": 18}, {"type": "text", "text": "二级菜单：CEMS监测预警；", "page_idx": 18}, {"type": "text", "text": "功能描述：", "page_idx": 18}, {"type": "text", "text": "CEMS报表导出：支持以不同频次小时、日、月、季度、年报表进行导", "page_idx": 18}, {"type": "text", "text": "出。", "page_idx": 18}, {"type": "text", "text": "2.3.1.4清洁运输 ", "text_level": 1, "page_idx": 18}, {"type": "text", "text": "2.3.1.4.1 运输台账：", "page_idx": 18}, {"type": "text", "text": "一级菜单：清洁运输；", "page_idx": 18}, {"type": "text", "text": "二级菜单：运输台账；", "page_idx": 18}, {"type": "text", "text": "功能描述：", "page_idx": 18}, {"type": "text", "text": "运输台账：依据HJ1321-2023标准要求，建立清洁运输总台账。系统对接厂区的门禁系统、称重/计量系统，整合记录运输物料信息、进厂车辆信息（含排放阶段、车辆类型等）以及运输方式。台账不仅涵盖公路运输，也应能纳入并管理厂区采用的所有运输方式的数据（如铁路、水路、皮带/传送带、管道等），确保数据符合标准格式要求；", "page_idx": 18}, {"type": "text", "text": "支持统计清洁运输比例，并且支持按照物料种类统计各个排放标准/运输方式的运输量、占比、车次、车辆等信息。", "page_idx": 18}, {"type": "text", "text": "2.3.1.4.2 进出厂记录：", "text_level": 1, "page_idx": 18}, {"type": "text", "text": "级菜单：清洁运输；二级菜单：进出厂记录；功能描述：", "page_idx": 18}, {"type": "text", "text": "进出厂记录：根据接入的门禁、计量数据，通过汇总区分呈现各个运输类型进出厂记录，可以按照大宗物料运输进出厂记录、非大宗物料运输进出厂记录、通勤记录等进行区分。", "page_idx": 19}, {"type": "text", "text": "2.3.1.4.3 车辆信息台账：", "text_level": 1, "page_idx": 19}, {"type": "text", "text": "级菜单：清洁运输；二级菜单：车辆信息台账；功能描述：", "page_idx": 19}, {"type": "text", "text": "车辆信息台账：车辆基础信息台账按货车、客车等分类管理，记录车牌、车型、排放标准、所属单位等关键信息。支持按日、周、月或自定义时间跨度对车辆信息进行统计分析（如各排放标准车辆占比），并将统计结果或车辆信息列表导出为Excel报表。", "page_idx": 19}, {"type": "text", "text": "2.3.1.4.4 厂内车辆台账：", "text_level": 1, "page_idx": 19}, {"type": "text", "text": "级菜单：清洁运输；二级菜单：厂内车辆台账；功能描述：", "page_idx": 19}, {"type": "text", "text": "厂内运输台账：针对在厂区内部运行的作业车辆（如叉车、内部转运车、非道路移动机械等）建立专项信息台账。支持管理人员根据车辆类型、车牌号码、车辆识别代码（VIN）、发动机号码、排放标准等多个关键字段进行快速查询和筛选。查询结果或完整的厂内车辆信息台账支持导出功能。", "page_idx": 19}, {"type": "text", "text": "2.3.1.5设备管理", "text_level": 1, "page_idx": 19}, {"type": "text", "text": "2.3.1.5.1 生产设备、治理设备、监测设备、环境质量监测设备：", "page_idx": 19}, {"type": "text", "text": "一级菜单：设备管理；", "page_idx": 19}, {"type": "text", "text": "二级菜单：生产设备、治理设备、监测设备、环境质量监测设备；", "page_idx": 19}, {"type": "text", "text": "功能描述：生产设备、治理设备、监测设备、环境质量监测设备：", "page_idx": 19}, {"type": "text", "text": "大屏端：根据设备类别和设备类型展示接入系统的设备信息及实时设备状态。  \nPC端：0提供对已接入设备的全面信息管理功能，包括新增、删除和修改设  \n备信息。0支持设备历史数据的可视化展示，可通过列表和折线图两种形式呈  \n现。0提供灵活的数据查询和导出功能，用户可根据设备类型、所属区", "page_idx": 19}, {"type": "text", "text": "", "page_idx": 20}, {"type": "text", "text": "域、设备状态以及设备编号/名称进行筛选和导出数据。", "page_idx": 20}, {"type": "text", "text": "2.3.1.5.2 监控设备：", "text_level": 1, "page_idx": 20}, {"type": "text", "text": "一级菜单：设备管理；", "page_idx": 20}, {"type": "text", "text": "二级菜单：监控设备；", "page_idx": 20}, {"type": "text", "text": "功能描述：", "page_idx": 20}, {"type": "text", "text": "监控设备：", "text_level": 1, "page_idx": 20}, {"type": "text", "text": "大屏端：根据设备类别和设备类型展示接入系统的监控设备信息及实时设备状态。PC端：0提供对已接入监控设备的全面信息管理功能，包括新增、删除和修改设备信息。0提供灵活的数据查询和导出功能，用户可根据设备类型、所属区域、设备状态以及设备编号/名称进行筛选和导出数据。", "page_idx": 20}, {"type": "text", "text": "2.3.1.5.3 视频墙：", "text_level": 1, "page_idx": 20}, {"type": "text", "text": "一级菜单：设备管理；", "page_idx": 20}, {"type": "text", "text": "二级菜单：视频墙；", "page_idx": 20}, {"type": "text", "text": "功能描述：", "page_idx": 20}, {"type": "text", "text": "视频墙：", "page_idx": 20}, {"type": "text", "text": "监控点位分类展示：将清洁运输门禁卡口、易产尘点、洗车平台等监控点位按区域进行清晰分类。免插件实时监控：无需安装任何浏览器插件，即可在平台直接播放实时视频监控画面。", "page_idx": 20}, {"type": "text", "text": "历史回放查询：支持对历史监控录像进行查询和回放。", "page_idx": 20}, {"type": "text", "text": "灵活的查询功能：支持根据设备类型、所属区域以及设备编号/名称对监控点位进行查询。", "page_idx": 21}, {"type": "text", "text": "2.3.1.6闭环管理", "text_level": 1, "page_idx": 21}, {"type": "text", "text": "2.3.1.6.1 设备报警：", "page_idx": 21}, {"type": "text", "text": "级菜单：闭环管理；二级菜单：设备报警；功能描述：", "page_idx": 21}, {"type": "text", "text": "", "page_idx": 21}, {"type": "text", "text": "设备报警：报警触发：当设备出现异常情况时，平台将发出报警。主要针对以下异常情况（可自定义配置）：设备离线、设备数据超标、其他自定义异常；报警配置：支持根据实际需要灵活配置报警规则，调整报警限值、设置异常持续时间、指定报警信息推送人员。", "page_idx": 21}, {"type": "text", "text": "2.3.1.6.2 产治不同步报警：", "text_level": 1, "page_idx": 21}, {"type": "text", "text": "一级菜单：闭环管理；二级菜单：产治不同步报警；功能描述：", "page_idx": 21}, {"type": "text", "text": "产治不同步报警：报警触发：当监测到生产设备正在运行，而相应的治理设备未运行的情况时，平台将发出报警；报警配置：支持自定义配置指定报警信息推送人员，设置产治不同步的异常持续时间。", "page_idx": 21}, {"type": "text", "text": "2.3.1.6.3 高频高幅报警：", "text_level": 1, "page_idx": 21}, {"type": "text", "text": "级菜单：闭环管理；二级菜单：高频高幅报警；功能描述：", "page_idx": 21}, {"type": "text", "text": "高频高幅报警：", "page_idx": 21}, {"type": "text", "text": "报警触发：当平台监测到TSP（总悬浮颗粒物）数据出现异常，且满足预设的高频率和高幅度波动要求时，将进行预警和报警。报警配置：支持自定义配置报警信息推送人员。", "page_idx": 21}, {"type": "text", "text": "大屏端能够展示所有报警信息，PC端支持对异常规则、推送人员等信息进行配置；移动端接收、执行、处理和反馈污染闭环管理流程功能，形成完整闭环管理。", "page_idx": 22}, {"type": "text", "text": "2.3.2 PC端 ", "text_level": 1, "page_idx": 22}, {"type": "text", "text": "2.3.2.1基础信息管理", "text_level": 1, "page_idx": 22}, {"type": "text", "text": "2.3.2.1.1 企业信息管理：", "page_idx": 22}, {"type": "text", "text": "一级菜单：基础信息管理；", "page_idx": 22}, {"type": "text", "text": "二级菜单：企业信息管理；", "page_idx": 22}, {"type": "text", "text": "功能描述：", "page_idx": 22}, {"type": "text", "text": "企业信息管理：支持自定义配置平台名称、logo、企业发展历程，能够根据企业实际情况，更换文字、视频等信息。", "page_idx": 22}, {"type": "text", "text": "2.3.2.1.2 区域管理：", "text_level": 1, "page_idx": 22}, {"type": "text", "text": "一级菜单：基础信息管理；", "page_idx": 22}, {"type": "text", "text": "二级菜单：区域管理；", "page_idx": 22}, {"type": "text", "text": "功能描述：", "page_idx": 22}, {"type": "text", "text": "区域管理：支持自定义分级配置工序、分厂、单元；支持设备绑定、能够通过区域名称进行筛选；支持新增、修改、删除、导入操作。", "page_idx": 22}, {"type": "text", "text": "2.3.2.1.3 用户管理：", "text_level": 1, "page_idx": 22}, {"type": "text", "text": "一级菜单：基础信息管理；", "page_idx": 22}, {"type": "text", "text": "二级菜单：用户管理；", "page_idx": 22}, {"type": "text", "text": "功能描述：", "page_idx": 22}, {"type": "text", "text": "用户管理：支持根据权限进行用户新增、修改、删除、重置密码、根据用户名称、手机号码、状态、创建时间进行筛选的操作，支持根据部门进行筛选。", "page_idx": 22}, {"type": "text", "text": "2.3.2.1.4 角色管理：", "text_level": 1, "page_idx": 22}, {"type": "text", "text": "一级菜单：基础信息管理；", "page_idx": 22}, {"type": "text", "text": "二级菜单：角色管理；", "page_idx": 22}, {"type": "text", "text": "功能描述：", "page_idx": 22}, {"type": "text", "text": "角色管理：支持根据角色进行权限管理，可以根据角色名称、权限字符、状态、创建时间进行筛选；支持新增、删除、导出、修改、数据权限配置、菜单权限配置、分配用户操作。", "page_idx": 23}, {"type": "text", "text": "2.3.2.1.5 部门管理：", "text_level": 1, "page_idx": 23}, {"type": "text", "text": "一级菜单：基础信息管理；", "page_idx": 23}, {"type": "text", "text": "二级菜单：部门管理；", "page_idx": 23}, {"type": "text", "text": "功能描述：", "page_idx": 23}, {"type": "text", "text": "部门管理：支持自定义新增、修改、删除部门，支持根据部门名称、状态进行筛选；部门支持分级展示，能够进行展开/折叠操作。", "page_idx": 23}, {"type": "text", "text": "2.3.2.1.6 国/省控管理：", "text_level": 1, "page_idx": 23}, {"type": "text", "text": "一级菜单：基础信息管理；", "page_idx": 23}, {"type": "text", "text": "二级菜单：国/省控管理；", "page_idx": 23}, {"type": "text", "text": "功能描述：国/省控管理：平台支持对国/省控站点进行隐藏和展示的操作。", "page_idx": 23}, {"type": "text", "text": "2.3.2.2污染源管理", "text_level": 1, "page_idx": 23}, {"type": "text", "text": "2.3.2.2.1 污染源基础信息：", "page_idx": 23}, {"type": "text", "text": "一级菜单：污染源管理；", "page_idx": 23}, {"type": "text", "text": "二级菜单：污染源基础信息；", "page_idx": 23}, {"type": "text", "text": "功能描述：", "page_idx": 23}, {"type": "text", "text": "污染源基础信息：系统支持个性化配置，后台支持自定义字段去配置污染源基础信息；不限于固定字段表格进行管理，后台支持根据污染源编号/名称、区域进行筛选，支持新增、导入、导出、编辑、删除操作。", "page_idx": 23}, {"type": "text", "text": "2.3.2.2.2 关联关系配置：", "text_level": 1, "page_idx": 23}, {"type": "text", "text": "一级菜单：污染源管理；", "page_idx": 23}, {"type": "text", "text": "二级菜单：关联关系配置；", "page_idx": 23}, {"type": "text", "text": "功能描述：", "page_idx": 23}, {"type": "text", "text": "关联关系配置：系统支持自定义配置各个污染源与设备之间的关联关系，允许对关联关系进行导入、导出操作。", "page_idx": 23}, {"type": "text", "text": "2.3.2.3设备信息管理", "text_level": 1, "page_idx": 24}, {"type": "text", "text": "2.3.2.3.1 设备管理：", "text_level": 1, "page_idx": 24}, {"type": "text", "text": "级菜单：设备信息管理；", "page_idx": 24}, {"type": "text", "text": "二级菜单：设备管理；", "page_idx": 24}, {"type": "text", "text": "功能描述：", "page_idx": 24}, {"type": "text", "text": "设备管理：系统支持自定义配置设备类型，设备字段，不限于固定字段八个进行管理，后台支持根据设备编号、设备状态、所属区域、设备状态进行筛选；支持导入、导出操作。", "page_idx": 24}, {"type": "text", "text": "2.3.2.3.2 报警记录：", "text_level": 1, "page_idx": 24}, {"type": "text", "text": "级菜单：设备信息管理；", "page_idx": 24}, {"type": "text", "text": "二级菜单：报警记录；", "page_idx": 24}, {"type": "text", "text": "功能描述：", "page_idx": 24}, {"type": "text", "text": "报警记录：系统支持在后台展示实时报警、历史报警。", "page_idx": 24}, {"type": "text", "text": "2.3.2.4清洁运输管理", "text_level": 1, "page_idx": 24}, {"type": "text", "text": "2.3.2.4.1 厂外运输：", "page_idx": 24}, {"type": "text", "text": "一级菜单：清洁运输管理；", "page_idx": 24}, {"type": "text", "text": "二级菜单：厂外运输；", "page_idx": 24}, {"type": "text", "text": "功能描述：", "page_idx": 24}, {"type": "text", "text": "厂外运输-基础信息配置：系统支持对公路运输、铁路运输等运输方式进行字段自定义配置；厂外运输-车辆信息配置：系统支持对公路运输中车辆信息字段进行自定义", "page_idx": 24}, {"type": "text", "text": "配置；", "page_idx": 24}, {"type": "text", "text": "厂外运输-物料信息配置：系统支持对物料进行管理，分三级物料进行展示，物料类型、物料种类、物料名称；支持配置该物料是否参与清洁运输比例运算，支持配置该物料所关联的进出厂信息是否展示。", "page_idx": 24}, {"type": "text", "text": "2.3.2.4.2 厂内运输：", "text_level": 1, "page_idx": 24}, {"type": "text", "text": "一级菜单：清洁运输管理；二级菜单：厂内运输；功能描述：", "page_idx": 24}, {"type": "text", "text": "厂内运输：系统支持自定义厂内运输车辆、非道路移动机械台账的自定义配置，可以进行新增、修改、删除等操作。", "page_idx": 25}, {"type": "text", "text": "2.3.2.5报警服务管理", "text_level": 1, "page_idx": 25}, {"type": "text", "text": "2.3.2.5.1 报警规则配置：", "page_idx": 25}, {"type": "text", "text": "一级菜单：报警服务管理；", "page_idx": 25}, {"type": "text", "text": "二级菜单：报警规则配置；", "page_idx": 25}, {"type": "text", "text": "功能描述：", "page_idx": 25}, {"type": "text", "text": "报警规则配置：系统支持自定义配置报警逻辑、报警推送人员，根据厂区实际情况，配置合适报警规则。", "page_idx": 25}, {"type": "text", "text": "2.3.3 移动端 ", "text_level": 1, "page_idx": 25}, {"type": "text", "text": "2.3.3.1移动应用", "text_level": 1, "page_idx": 25}, {"type": "text", "text": "2.3.3.1.1 首页：", "page_idx": 25}, {"type": "text", "text": "一级菜单：移动应用；", "page_idx": 25}, {"type": "text", "text": "二级菜单：首页；", "page_idx": 25}, {"type": "text", "text": "功能描述：", "page_idx": 25}, {"type": "text", "text": "首页：手机移动端能够查看国控站数据对比、清洁运输数据首页呈现；", "page_idx": 25}, {"type": "text", "text": "2.3.3.1.2 运行记录：", "text_level": 1, "page_idx": 25}, {"type": "text", "text": "一级菜单：移动应用；", "page_idx": 25}, {"type": "text", "text": "二级菜单：运行记录；", "page_idx": 25}, {"type": "text", "text": "功能描述：", "page_idx": 25}, {"type": "text", "text": "运行记录：移动端支持查看生产设备、治理设备、监测设备、监控设备等的运行情况，包括设备的开关状态、运行模式、关键参数值等。", "page_idx": 25}, {"type": "text", "text": "2.3.3.1.3 监测数据：", "text_level": 1, "page_idx": 25}, {"type": "text", "text": "一级菜单：移动应用；", "page_idx": 25}, {"type": "text", "text": "二级菜单：监测数据；", "page_idx": 25}, {"type": "text", "text": "功能描述：", "page_idx": 25}, {"type": "text", "text": "监测数据：移动端支持查看各类型监测设备的实时数据、历史数据；历史数据支持查看分钟数据、小时数据、天数据。", "page_idx": 25}, {"type": "text", "text": "2.3.3.1.4 我的待办：", "text_level": 1, "page_idx": 26}, {"type": "text", "text": "一级菜单：移动应用；", "page_idx": 26}, {"type": "text", "text": "二级菜单：我的待办；", "page_idx": 26}, {"type": "text", "text": "功能描述：", "page_idx": 26}, {"type": "text", "text": "我的待办：移动端能够查看和发起待办任务，可以接收设备异常报警，查看和反馈报警情况，实现闭环管理。", "page_idx": 26}, {"type": "text", "text": "2.3.4 平台接口", "text_level": 1, "page_idx": 26}, {"type": "text", "text": "*******数据接口功能", "text_level": 1, "page_idx": 26}, {"type": "text", "text": "功能描述：满足超低排放各类数据接入需求，通过多种协议实现数据对接。支持 HJ212-2017、OPC-DA/UA、Modbus、TCP/IP、HTTP等协议。", "page_idx": 26}, {"type": "text", "text": "2.4重点功能描述", "text_level": 1, "page_idx": 27}, {"type": "text", "text": "2.4.1 管理驾驶舱", "text_level": 1, "page_idx": 27}, {"type": "text", "text": "管理驾驶舱对厂区整体情况和管理所需常用模块进行体现，整体内容以3D地图为载体，进行各分厂生产设施、治理设施、监控监测设施和环境治理监测设备在地图上进行体现。管理驾驶舱包括分厂污染实时排名、实时报警信息和设备位置信息。", "page_idx": 27}, {"type": "image", "img_path": "images/a6b91ef8ace02ee146d239624dfc177671d8de290bf0e0fe519b8b7b232b00ef.jpg", "image_caption": ["管理驾驶舱示意图"], "image_footnote": [], "page_idx": 27}, {"type": "text", "text": "******* 实时报警信息 ", "text_level": 1, "page_idx": 27}, {"type": "text", "text": "实时报警信息对设备超标情况和报警情况进行展示，展示内容包括定位信息、设备名称、报警时间、报警类型、处理进度等信息。点击定位可在地图中高亮显示设备所在位置，并查看设备实时状态和对应视频监控信息。", "page_idx": 27}, {"type": "text", "text": "清洁运锁 有经积管控 环保管控治一体化平台 首真 环境质装 设备台售 报警管理防史报警销息 实时报警设备类型 202401-1201.00：2024-0120:0名/号绿时间定制化报警 1 产地不批乡#警 14站 2624-0-121:0021 产品不间步警 生产备：理设生产不同营 16 冶理设鱼维护2 产地不同乡报警 14房结 2624-0-12135021 产治不同多按警 生产设备：7建设备：治区城水，生：产施不警 16 冶售设备维护车辆信息报警 产地不间多报# 202-0-1212 产地不我步按售 生产备设区域全产地不步督警 16 治蓬设自维护产地不#乡## 2024-09-1213:00:21 生产设备7建设区路产不步警 16备 ： 产地不#乡## 2024-0-123102 生产设 16产治不同步报警 产地不同步警 产地不同步投警 74集站 202-0121:02 2024-09-121:021 产治不同步形警 生产设备：7：设备：区城营水生路：产不少 生产7设备：治区城国生通产地不步警 16 16 治理设备维护 治理设备维护床汽车#警 ： 产地不同多报警 2024-09-1213:00.21 产治不核多报警 生产设备：7#；设备：治区城意水浴，：产不用营 16 治理安香物护产地不员步#警 2024-0-1213:002 生产：：区路产不 16 治建设备#产10 产地不间乡## 1#社 2024-09-1213:0021 生产备设产 1611 产地不同乡#警 14晚址 2024-09-1213:00.21 生产设备：7设备：区水：生产不量 16 治理设备维户O.000.000000无级价暂控 有限积警控 环保管控治一体化平台 首页 环境质量 设备台粮 保保管理实时报警销息 历免按警乐宝时间定制化报警 0 产地不我乡报警 14 2024-03-1213:0021 产出不间步## 生产备：设备：理区水活通：产不 16 法理设集地产® 2 产治不得步警 1级证 2024-03-1213:021 产治不同步服装 生产设备：7带：设备：地建区城理注水站：生者：产治不警 16 油锁设备维护 治缓设备快护厂 ® 1 产#不提步售 1 2020 广治不同步警 生产设备：：设备：理区法水治不警 16 洁度设备维护4 产治不同乡#警 生产设备：：建区里洁水：产治不用步 16 #理设备#护。 S 产不我步#警 14位 202-03-1213:0021 产治不#步银警 生产7区不 166 产出不提乡警 14 202-09-1213:0021 产治不限步报警 生产设备：设量建区理水产不 16 丑理设鱼维护111 ® 7 产治不向乡#警 202-03-121300-2 产治不间乡报警 生产设备7设不 16 冶湾议鱼维护 出择设备护来汽车警 ® 产助不同多服警 2024-03-1213:0021 产治不用多报警 生产设备：7设备：建区城洁水生器产治不用步管 10 #理议备维护 冶理设备维护® 9 产不限#警 2024-03-1213.002 产治不同步指营 生产：区路产警 16 出#设备维护® 10 14u 202-03-121:062 严出不间步#警 生产设区产不步 16 油建安鱼维护11 149 2024-03-1213:0021 产治不同步警 生产设备7：设：区水产不步警 16 油理设备维护 2设备护..800.000000", "page_idx": 27}, {"type": "text", "text": "2.4.1.2 设备位置信息", "text_level": 1, "page_idx": 28}, {"type": "text", "text": "展示所有接入平台设备的位置信息及基本情况，点击设备即可查看当前设备的实时数据、历史数据及监控情况。", "page_idx": 28}, {"type": "text", "text": "2.4.2 无组织监管", "text_level": 1, "page_idx": 28}, {"type": "text", "text": "无组织监管功能主要针对厂区无组织排放的管控治情况进行管理和控制，按照政策对无组织管控的要求，进行无组织污染源管理、环境质量监测、设备台账、联动治理和决策分析模块的设计，确保企业在面对专家检查时能够自证清白，在日常监管中起到监管控制的作用。", "page_idx": 28}, {"type": "text", "text": "企业无组织排放受到物料特性、生产工艺、气象条件、气流扰流等多种因素的综合影响，治理难度大。同时，企业无组织污染源众多，涉及到物料的堆取、物料的转运、物料的加工、道路运输等，某一环节的无组织排放与其他环节均有一定关系，因而通过单点处理很难控制整个厂区的无组织排放，现有的单点处理路线，头痛医头脚痛医脚，既增加了企业的电力能耗，又增加了企业运维管理的人力成本。", "page_idx": 28}, {"type": "text", "text": "超低排放管控治一体化平台无组织排放管控版块能够将无序的无组织排放，通过监测与分析生成全面统筹协调源头综合系统控制治理的治理系统。实现企业无组织排放管理、控制、治理以及公共服务的综合一体化的管理系统。", "page_idx": 28}, {"type": "text", "text": "2.4.2.1 污染源管理", "text_level": 1, "page_idx": 28}, {"type": "text", "text": "超低排放管控治一体化平台无组织排放源清单按照生产工艺过程、物料输送、物料封闭存储分类，将无组织污染源受控情况呈现在工艺流程图或3D厂区地图中，直观地、实时地呈现排放源的受控点数据。", "page_idx": 28}, {"type": "text", "text": "平台无组织排放源清单也可以用表格形式显示，方便快捷地对无组织排放源进行管理。展示污染源关联生产设备、治理设备、监测设备的运行情况及监测数据。", "page_idx": 28}, {"type": "text", "text": "平台通过对污染源与相关监测设备进行关联，能够实时对污染源及污染源所在分厂进行排放及污染分析，为企业内部考核提供管理数据。", "page_idx": 28}, {"type": "image", "img_path": "images/d418e0e8bf6935cf6e9b13482a84aaa29f0e722c2a20ad7e6f39c3e9fe0f0ab9.jpg", "image_caption": [], "image_footnote": [], "page_idx": 29}, {"type": "image", "img_path": "images/a8cd833c35b65512d2e038360c42dcf82c462eb796e9760b6ca828a64b859d6e.jpg", "image_caption": [], "image_footnote": [], "page_idx": 29}, {"type": "image", "img_path": "images/d209d769d4d2e03f1befcd9023c29e59a7b713e03fa31b52a34c0bf2481e428d.jpg", "image_caption": ["区污染源清单示意图"], "image_footnote": [], "page_idx": 29}, {"type": "text", "text": "2.4.2.2 环境质量监测", "text_level": 1, "page_idx": 29}, {"type": "text", "text": "超低排放管控治一体化平台通过展示全厂的环境质量监测设备的部署位置、实时监测数据，展示企业内部环境变化。并通过将企业所在市内国控点/省控点数据进行采集并呈现，将国控点/省控点与厂区环境数据以曲线图或者列表的形式进行对比，保证企业环境质量对厂外环境未造成影响。", "page_idx": 29}, {"type": "text", "text": "平台对环境质量监测设备的监测数据进行分析整理，按照查询需求展示生成的污染日历图、月历图及年历图。", "page_idx": 29}, {"type": "image", "img_path": "images/9938355c6a5537d52d6cffd388be30b023baea42b89a05987db6ede597298e14.jpg", "image_caption": [], "image_footnote": [], "page_idx": 30}, {"type": "image", "img_path": "images/d6268c9d8ebdcc2da8dfc87dd04e0f4508ad8dd44cdd815b3bb88d9dced75dbe.jpg", "image_caption": ["环境空气质量检查示意图"], "image_footnote": [], "page_idx": 30}, {"type": "text", "text": "2.4.3 设备台账", "text_level": 1, "page_idx": 30}, {"type": "text", "text": "超低排放管控治一体化平台将接入平台内的生产设备、治理设备、监测设备、监控设备等设备的运行数据及监测数据进行保存，并能够按照实际需求进行查询或导出成Excel文件。既能便于企业在做等级评定时提供环境治理证据，又能生成企业内部管理报表。", "page_idx": 30}, {"type": "text", "text": "平台具备污染限值甲方自定义设置功能，企业可根据自身内部环保压力调整限值。在监测设备污染值超过限值后，平台会自动进行报警。", "page_idx": 30}, {"type": "text", "text": "2.4.3.1 联动治理", "text_level": 1, "page_idx": 30}, {"type": "text", "text": "超低排放管控治一体化平台通过针对生产工艺过程、物料输送、物料封闭存储、厂区环境不同的污染类型的污染特性，制定不同的治理方案。针对物料储存环节，制定雾炮与颗粒物监测仪表数据联动，设定“工作计划\"联动模式及鹰眼视觉AI联动，让雾炮根据需求自动抑尘作业。针对物料输送、生产工艺过程的产尘特性，采用颗粒物联动治理，当污染源监测值超标后，通过异常报警，改变治理设备运行负荷。针对厂区环境，对未洗车等违规行为进行识别并抓拍，保证厂内环境保持良好，若异常超标，平台自动进行环卫车调度，降低环境风险。", "page_idx": 30}, {"type": "text", "text": "", "page_idx": 31}, {"type": "image", "img_path": "images/0f1d64d997af6356f5ae2765de8c8761a163831dcec614d346e779e019cd2e86.jpg", "image_caption": ["物料储存治理"], "image_footnote": [], "page_idx": 31}, {"type": "image", "img_path": "images/c72cee72b1edde5236016f327dc45b26bb42e22996e51cdcced0412a8d797070.jpg", "image_caption": ["雾炮详情"], "image_footnote": [], "page_idx": 31}, {"type": "image", "img_path": "images/847d5690025d4968542bfd4553cd6a1f52dd0bfb6467dbc62f97560e9afd29f3.jpg", "image_caption": ["环卫车管理"], "image_footnote": [], "page_idx": 31}, {"type": "text", "text": "2.4.4 有组织监管", "text_level": 1, "page_idx": 31}, {"type": "text", "text": "有组织治理的监测拥有成熟的监测体系，是环保局考核企业的重要标准之一。有组织监测数据是进行企业核查时专家关心的重点。有组织监管主要包含有组织源清单和CEMS监测清单功能，能够对厂区整体的排放情况进行监控和报警，防止超标排放。", "page_idx": 31}, {"type": "text", "text": "2.4.4.1 有组织产治同步", "text_level": 1, "page_idx": 32}, {"type": "text", "text": "有组织排口清单是将全厂的重点监测排放口和对应的生产治理设备进行同屏展示。", "page_idx": 32}, {"type": "image", "img_path": "images/3319dae544a89aead92b6c75a13838741f409cbcc7e44e9c11cba4dca27ebff0.jpg", "image_caption": ["有组织产治同步示意图"], "image_footnote": [], "page_idx": 32}, {"type": "text", "text": "2.4.4.2 CEMS监测清单 ", "text_level": 1, "page_idx": 32}, {"type": "text", "text": "将全厂接入的有组织排放口监测数据通过列表的形式进行显示，并通过设备上报实测值与限值进行比较，对监测达标率进行统计。选取关键参数监控，进行卡边报警。", "page_idx": 32}, {"type": "image", "img_path": "images/a2426988c6ef65afe1e6dec45bf9d216214eae8358d73f00d75c3fb46d83eed7.jpg", "image_caption": ["CEMS排口示意图"], "image_footnote": [], "page_idx": 32}, {"type": "text", "text": "2.4.5 清洁运输 ", "text_level": 1, "page_idx": 32}, {"type": "text", "text": "清洁运输包括厂内运输和场外运输两部分，对出入厂区的车辆和厂内非道路移动车辆进行管理和登记，了解来厂外部车辆、运输车辆、厂内非道路移动机械等车辆设备的实际情况，确保车辆情况符合政策要求中清洁运输的要求。", "page_idx": 32}, {"type": "text", "text": "2.4.5.1 厂外运输管理", "text_level": 1, "page_idx": 32}, {"type": "text", "text": "通过整合门禁系统、计量系统、监控系统，以展现企业的实时进厂车辆记录、实时进厂车辆状态、运输车辆合规统计、大宗物料和产品运量统计、运输结构及清洁运输比率、厂外运输历史台帐、实时监控图像（门禁、过磅、洗车台）、车辆详细信息一图像显示、车辆详细信息一关联信息方面的内容。", "page_idx": 32}, {"type": "text", "text": "厂外运输管理系统与厂区已有物流系统结合实现门禁管理功能，通过调取物流系统的数据接口，获取进厂物流车辆的信息。智能门禁管理平台对过门禁的车辆信息及物料信息进行记录。", "page_idx": 33}, {"type": "image", "img_path": "images/8d860f0a2e210245d4d9684495da493cabf29e7cb1cdfa47b62fae9fcaaa2b6c.jpg", "image_caption": ["厂外运输管理示意图"], "image_footnote": [], "page_idx": 33}, {"type": "text", "text": "2.4.5.2 厂内运输管理", "text_level": 1, "page_idx": 33}, {"type": "text", "text": "通过录入厂内运输车辆、非道路移动机械车辆，以展示企业的在运行车辆清单、厂内运输车辆结构、厂内运输编码登记和排放合规情况、非移动道路车辆结构、非移动道路机械编码登记和排放合规情况、厂内车辆台账、厂内车辆实时位置与状态标注、单一车辆关联信息内容等方面的情况。", "page_idx": 33}, {"type": "image", "img_path": "images/0c0d5da03649d3e37190a9b86783f5496109720bc40ff960ad5a7a881f1ece5e.jpg", "image_caption": [], "image_footnote": [], "page_idx": 33}, {"type": "text", "text": "2.4.6 闭环管理", "text_level": 1, "page_idx": 33}, {"type": "text", "text": "在平台内查看具体的报警详情信息，包括监测设备名称、报警类型、异常项、异常值、报警开始/恢复时间、报警时长、报价原因和处理措施等信息。", "page_idx": 33}, {"type": "image", "img_path": "images/f5c1451dd5fe5961de2a467fb89aa0c4fe4bf6be3da371ccb40f051c01504d26.jpg", "image_caption": ["高频报警设备展示"], "image_footnote": [], "page_idx": 34}, {"type": "image", "img_path": "images/01bd82528d92c052577a91221e7ad080c3561b23b8f5ffb512f4bf22186153d0.jpg", "image_caption": ["设备报警及处理管理"], "image_footnote": [], "page_idx": 34}, {"type": "table", "img_path": "images/4b95c4fd17606e89b42458b7ce7a236bdf382862b6a354b64c10a16fa82097ba.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>定位 序号</td><td>报警名称 设备名称</td><td></td><td>报警时间</td><td>报警类型</td><td>报警详情</td><td>持续时长（min）</td><td>报警原因</td><td>处理措施</td></tr><tr><td>? 1</td><td>产治不同步报警</td><td>1#主要生产设备</td><td>2024-07-2215:32:00</td><td>产治不同步报警</td><td>生产设备：1#主要生产设备 0发 产治不同步报警</td><td>4300.25</td><td>无</td><td>无</td></tr><tr><td>2</td><td>产治不同步报警</td><td>测试生产111</td><td>2024-07-2215:32:02</td><td>产治不同步报警</td><td>生产设备：测试生产111 75发 不同步报警</td><td>4300.21</td><td>无</td><td>无</td></tr><tr><td>? 3</td><td>产治不同步报警</td><td>大型测试设备01</td><td>2024-07-2215:32:02</td><td>产治不同步报警</td><td>生产设备：大型测试设备01 ，治理设备：1#雾炮，雾炮07 15触发产治不同步报警</td><td>4300.21</td><td>无</td><td>无</td></tr></table>", "page_idx": 34}, {"type": "text", "text": "产治不同步报警", "text_level": 1, "page_idx": 34}, {"type": "text", "text": "2.4.7 移动端 ", "text_level": 1, "page_idx": 34}, {"type": "text", "text": "手机移动端显示接入平台的无组织排放治理设备运行状态与监测设备的监测数据，使管理人员能够随时随地对环境监测情况进行实时查看，利用碎片化时间进行管理工作。", "page_idx": 34}, {"type": "text", "text": "手机移动端具备监测预警和故障报警功能，自动将点位、标准站、超标报警的详细信息、故障的设备信息推送至相关责任人的移动端APP，使管理人员能够在手机上及时发现问题、处理问题。", "page_idx": 34}, {"type": "text", "text": "0 1:20 PM 77% 169%1122 1:20PM 77  \n< 报警记录 < 北京易玖生态环境有限公司 8 < 治理设备清单监测设备清单监控设备清单 Q设备费型 v 所属分厂 时间选择 v 设备类型 斯属分广 运行状态 Y车辆报警提醒超标 故障 陕钢汉钢-车辆信息不完整异常报警 9条新消息 E-001西厂原料南侧2020-09-06 14:23:56(56min) 实时监测设备编号： E-0001 车辆编码： 陕B56093 TSP 温度 湿度设备名称： 西厂原科相南雾炮 报警类型： 发动机号码未录入 205ug/m 27℃ 48%RH所属分厂： 西厂原科棚 时间： 2021-12-1411:19:56TSP.盗测值234报警值50g/m 地点： 门禁-东南门入口备注： 请及时安排人员到异常地点查看 302020-09-06 14:23:56(56min)设备编号： E-0001 设备报警通知 … 200设备名称： 西厂原科相南雾炮所属分厂： 西厂原科棚 新泰钢铁-CEMS监测设备触发报警 100  \n报暨详情： TSP:监测值234,报警值50ug/ma  \n处理港度： 颗粒物联动已启动 设备ID： CEMS-1 02408101214182020-09-06 14:23:56(56min) 设备名称： 2×100m2烧结脱硝塔出口 E-0009 头进预温机 据正常 180m烧结厂报警类型： 超标：SO、实测：37.820（mg/m）、 E-0010 原1皮机尾 数扭正常 180m结厂设备编号： E-0001 限值：35（mg/m）  \n设备名称： 西厂原科棚南雾炮 E-0011 原1皮带主体 据超标 180m烧结厂  \n所属分厂： 西厂原科棚 报警时间： 2021-12-14 11:21:13报暨详情： TSP:监测值234,报置值50ug/m3 备注： 请在APP或平台查看报警详情 E-0012 预料机 网高 180ml烧厂处理港度 颗粒物联动已启动", "page_idx": 35}, {"type": "text", "text": "2.4.8 视频广场", "text_level": 1, "page_idx": 35}, {"type": "image", "img_path": "images/d5f4849c20c485fa54832565a6cd886858df5e560796f6f8af3754d22bb12a5b.jpg", "image_caption": [], "image_footnote": [], "page_idx": 35}, {"type": "text", "text": "2.4.9 建模效果示意图", "text_level": 1, "page_idx": 35}, {"type": "text", "text": "3D手工建模建成效果示意图：", "page_idx": 35}, {"type": "image", "img_path": "images/a552b09cf663e499937716696f80c0b0c5ef7e245fc47c4e82f7f79f2694a5be.jpg", "image_caption": [], "image_footnote": [], "page_idx": 35}, {"type": "text", "text": "3 超低排放监测监控体系建设", "text_level": 1, "page_idx": 36}, {"type": "text", "text": "3.1 厂区环境空气质量微站布设 ", "text_level": 1, "page_idx": 36}, {"type": "text", "text": "环境空气质量微站监测设备数量须满足超低排放评估要求，对厂界、主要货运道路、重点区域PM10、PM2.5等8因子进行监测。", "page_idx": 36}, {"type": "text", "text": "3.2 排放源粉尘TSP浓度监测布设 ", "text_level": 1, "page_idx": 36}, {"type": "text", "text": "以超低排放预评估调研结果结合各分厂车间现有粉尘监测监控现状，按照超低排放粉尘源监测要求新增粉尘源泵吸式TSP监测仪表，监测设备数量须满足贵钢超低排评估要求，并对喷煤等有防爆安全要求的工艺环节预留配套防爆型粉尘监测仪。", "page_idx": 36}, {"type": "text", "text": "3.3 重点污染区域高清视频监控布设 ", "text_level": 1, "page_idx": 36}, {"type": "text", "text": "依据超低排放政策要求，针对现场实际情况进行布设规划设计，新增部署视频监控设施，监控设备数量须满足超低排评估要求。根据厂区周边污染排放情况，对重点区域进行不间断监控，及时发现污染并可查询历史记录情况，确保及时管控烟尘外溢情况。", "page_idx": 36}, {"type": "text", "text": "上述设备通过有线传输方式将视频监控画面上传至超低排放管控治一体化平台，方便企业管理人员对厂区内情况实时监控。", "page_idx": 36}, {"type": "text", "text": "3.4 物料湿度等级监测仪监测布设 ", "text_level": 1, "page_idx": 36}, {"type": "text", "text": "根据目现场物料的物料运输情况，针对部分湿料运输区域部署物料湿度等级监测仪。在球团车间精矿粉皮带、球团煤棚煤皮带、氧化锌车间输送皮带、喷煤车间煤皮带区域进行物料湿度等级监测仪部署。", "page_idx": 36}, {"type": "text", "text": "4 清洁运输门禁管理系统建设", "text_level": 1, "page_idx": 36}, {"type": "text", "text": "清洁运输方面，企业需自我举证证明评估周期内进出厂车辆符合国六以上或新能源要求：依据提供运输车辆信息台账及门禁系统中车辆信息，比对车辆排放标准符合性和数量一致性，厂外清洁运输部分监管系统构建包括车辆基础信息、门禁和视频系统、计量系统三部分，厂内部分主要是厂内车辆和非道路移动机械台账的呈现。", "page_idx": 36}, {"type": "text", "text": "4.1 门禁管理系统建设", "text_level": 1, "page_idx": 36}, {"type": "text", "text": "针对全厂门岗或进出口进行优化规划设计，综合考虑不同功能区界面机厂界划分、大宗物料与成品物流线路，建设清洁运输门禁系统硬件和软件系统，并与磅秤计量系统记录数据打通，满足超低排放清洁运输车辆门禁及大宗原物料及产品运输台账管控要求。", "page_idx": 37}, {"type": "text", "text": "易玖生态对全厂门岗、进出口进行优化整合，综合考虑不同功能区界面机厂界划分、大宗物料与成品物流线路，依据最新门禁政策《重点行业移动源监管与核查技术指南》（HJ1321-2023）建设和改造。在物流大门安装门禁及视频系统，包含卡口相机、高点视频监控、LED显示屏、道闸（含防砸雷达）、操作电脑、违规报警装置、关联计量系统数据、信息审核和校验及门禁运输台账软件功能开发等全套门禁系统。", "page_idx": 37}, {"type": "text", "text": "新建门禁系统能够有效的对国六及以上车辆进行管理，杜绝国六以下车辆进入厂区，达到自动管控的目的。车辆门禁系统卡口相机自动识别车辆车牌，视频监控存储1年，图像数据信息自动保存记录存储24个月。门禁系统数据通过有线传输方式上传至一体化平台。", "page_idx": 37}, {"type": "text", "text": "建立数据库，不断地完善系统平台的车辆信息数据库。易玖生态提供接口程序（二维码录入、微信小程序等)，企业客户通过管理手段对进厂车辆收集其“车牌号、排放标准、车辆类型、车辆识别代号、发动机号码、行驶证照片、车辆随车清单照片、车辆环保标识照片、运营许可证照片\"等资料，导入易玖生态系统建立车辆数据库，车辆进出厂时，门禁系统视频摄像头自动识别车辆车牌，核对数据库中信息，对符合要求的抬杆放行，并实时保存信息。", "page_idx": 37}, {"type": "text", "text": "此数据库的建立是个不断积累的过程，系统运行初期由于车辆数据库信息不全可能会导致系统无法完全识别，并导致异常操作，均为正常现象。要求企业客户确保车辆信息录入的准确性和数据更新的及时性。", "page_idx": 37}, {"type": "text", "text": "5 料场内部无组织抑尘治理建设", "text_level": 1, "page_idx": 37}, {"type": "text", "text": "5.1 料场治理总体建设方案", "text_level": 1, "page_idx": 37}, {"type": "text", "text": "根据《钢铁企业超低排放改造技术指南》要求：间歇式、非固定的产尘点，可采用干雾等抑尘技术，雾滴直径宜小于 $3 0 \\mu \\mathrm { m }$ 。抑尘作业需快速精准联动，响应时间宜小于5s，喷雾须完全覆盖扬尘区域。", "page_idx": 37}, {"type": "text", "text": "对于料场内部未配备满足超低排放改造要求的收尘或抑尘措施、抑尘精准联动等措施的情况，在料场内规划建超细雾炮、高效干雾抑尘系统、雾炮智能AI联动系统及雾炮平台等配套措施。", "page_idx": 37}, {"type": "text", "text": "", "page_idx": 38}, {"type": "text", "text": "整套系统在随机移动污染发生短时间内进行雾炮抑尘，5秒内判断污染状况并联动治理设备进行治理，迅速压制粉尘，肉眼无可见烟尘上逸，测量作业区域粉尘浓度 $\\leq 8 \\mathrm { m g } / \\mathrm { N } \\mathrm { m } ^ { 3 }$ 。系统必须满足污染识别准确率 $2 9 9 \\%$ ，抑尘设备控制同步率 $29 8 \\%$ ，漏报率 ${ < } 2 \\%$ ，误报率 ${ < } 2 \\%$ 。", "page_idx": 38}, {"type": "text", "text": "5.2 雾炮鹰眼智能联动治理", "text_level": 1, "page_idx": 38}, {"type": "text", "text": "系统整合安装在大棚内的监测、监控设备，实现无人化智能治理。包含以", "page_idx": 38}, {"type": "text", "text": "下三级灵活配置治理模式：", "page_idx": 38}, {"type": "text", "text": "（1）雾炮与颗粒物监测仪表数据联动，用户可以自动设置颗粒物超标超限数值，设置雾炮喷射时长。监测数据超限或超标自动启动关联的雾炮进行抑尘作业。该场景适用于所有工作区域。", "page_idx": 38}, {"type": "text", "text": "（2）设定雾炮“工作计划”，让雾炮根据物料进厂频次自动抑尘作业，用户可以自己设定作业时间、时段、时长等。通过合理分配雾炮的作业频次，不仅可以起到抑尘的作用，还可以维持物料湿度的稳定，在生产和环保之间达到平衡。该场景用于物料害怕长时间喷射、积水，不需要考虑工作车辆，需要定点定时工作的区域。", "page_idx": 38}, {"type": "text", "text": "（3）AI联动。AI智能联动是指在棚内智能雾炮中智能AI识别系统实时捕捉监控画面，智能识别画面中是否存在扬尘行为。若画面中存在扬尘行为，则自动向雾炮发送扬尘位置，雾炮控制器控制设备快速启动，并发送旋转指令使设备转动到指定角度或开启对应区域的天雾进行抑尘工作。该场景适用于物料不怕潮湿，且工作车辆频繁区域。", "page_idx": 38}, {"type": "text", "text": "5.2.1 雾炮AI鹰眼联动工作原理", "text_level": 1, "page_idx": 38}, {"type": "text", "text": "5.2.1.1目标检测、动态捕捉", "text_level": 1, "page_idx": 38}, {"type": "text", "text": "通过AI识别监控摄像机对料棚内指定区域进行监控覆盖，对料棚内作业设备进行识别判断，利用视觉搜索获取料棚内车辆装卸等实时采集生产作业信息，实现作业目标检测，车辆类型识别准确度极高，为系统提供信息数据，同时也可记录治理过程。", "page_idx": 38}, {"type": "text", "text": "5.2.1.2视觉识别、污染定位", "text_level": 1, "page_idx": 39}, {"type": "text", "text": "通过前端视频监控实时采集设备提供的图像、视频信息，结合图像识别算法系统，对正在作业的区域进行分析，识别车辆行为并精准定位粉尘污染位置，对污染行为进行判定，并准确定位污染位置。", "page_idx": 39}, {"type": "text", "text": "5.2.1.3系统决策、雾炮联动", "text_level": 1, "page_idx": 39}, {"type": "text", "text": "将识别系统提供的位置信息及污染行为判定情况进行系统分析、决策，并形成执行命令通过以太网通讯下达至对应区域雾炮控制PLC，控制雾炮设备的启停、转动、俯仰等功能启动，达到治理设备与识别控制系统的联动结合，实现精准抑尘。", "page_idx": 39}, {"type": "image", "img_path": "images/4024e764a9b3be18f14643731dfedeab964142cf200f21ce3ad4e883b8d1aabf.jpg", "image_caption": [], "image_footnote": [], "page_idx": 39}, {"type": "image", "img_path": "images/d4ef742e4a9145c99c61e062c2fc0163cc84aa381fbeb6dd73954872c36e45e9.jpg", "image_caption": [], "image_footnote": [], "page_idx": 39}, {"type": "text", "text": "根据现场工况，三种治理模式既能单独进行智能化治理，也能协同工作，达到突发情况下，智能化抑尘的目的。在原料车间、机械化原料场配套智能治理AI鹰眼摄像头，实现料场扬尘的精准治理，打造企业亮点工程。", "page_idx": 39}, {"type": "text", "text": "5.3 干雾/微雾治理建设方案", "text_level": 1, "page_idx": 40}, {"type": "text", "text": "干雾/微雾抑尘治理区域主要包括：原料棚汽车喂料口、料仓汽车喂料口、皮带机头、移动布料小车、皮带落料口、链板机、轧机等。", "page_idx": 40}, {"type": "text", "text": "根据现场情况进行设计和改造，布置干雾抑尘雾帘阻止粉尘向外逃逸，控制方式为手动 $^ +$ 自动（安装红外触发感应装置联动）。", "page_idx": 40}, {"type": "text", "text": "5.4 料场分控中心建设方案", "text_level": 1, "page_idx": 40}, {"type": "text", "text": "根据料场治理方案及料场位置区域，新建对应的分控中心。分控中心包含分控硬件和数据传输系统，将无组织排放相关除尘、抑尘等设备运行状态与现有采集的生产数据，统一传输至超低排放管控治一体化平台中集中管理。", "page_idx": 40}, {"type": "text", "text": "依据分控中心设备配置情况，设置相应的分控中心配置间，满足现场工作人员日常管理需求，同时利旧厂区原有主控室。", "page_idx": 40}, {"type": "text", "text": "5.5 料场水处理建设方案", "text_level": 1, "page_idx": 40}, {"type": "text", "text": "根据料场治理方案及料场位置区域，建设水处理间。", "page_idx": 40}, {"type": "text", "text": "6 数据采集、接入及管理建设方案", "text_level": 1, "page_idx": 40}, {"type": "text", "text": "6.1 有组织数据采集及接入", "text_level": 1, "page_idx": 40}, {"type": "text", "text": "$\\textcircled{1}$ 有组织生产数据采集：通过对选取的有组织生产设备数据的采集，为超低排放管控治一体化平台中有组织数据集中管理及分析展示模块的开发奠定基础，确保采集的有组织在线监测数据具有一年存储记录，并能够随时调阅至少一年内任意时段数据进行查看。", "page_idx": 40}, {"type": "text", "text": "$\\textcircled{2}$ 有组织治理数据采集：通过对选取的有组织治理设备数据的采集，为超低排放管控治一体化平台中有组织数据集中管理及分析展示模块的开发奠定基础，确保采集的有组织在线监测数据具有一年存储记录，并能够随时调阅至少一年内任意时段数据进行查看。", "page_idx": 40}, {"type": "text", "text": "$\\textcircled{3}$ 有组织在线监测（CEMS）数据采集：通过对有组织在线监测数据的采集，为超低排放管控治一体化平台中有组织数据集中管理及分析展示模块的开发奠定基础，确保采集的有组织在线监测数据具有一年存储记录，并能够随时调阅至少一年内任意时段数据进行查看（包括手机移动端）。", "page_idx": 40}, {"type": "text", "text": "6.1.1 DCS政策要求", "text_level": 1, "page_idx": 41}, {"type": "text", "text": "超低排放及重点行业应急减排相关政策明确规定：烧结机机头、烧结机机尾、自备电厂等排气筒均需安装烟气排放连续监测系统（CEMS），相关废气治理设施配套分布式控制系统（DCS)。废气治理设施DCS应记录企业环保设施运行状况及相关生产过程主要参数。将各除尘/脱硫/脱硝设施所有运行参数、CEMS 在线监测数据、以及反映生产负荷和设备启停的主要生产工艺参数集中管理，并具备保存一年以上历史数据的能力，任意参数曲线能够组合至同一个界面中查看。", "page_idx": 41}, {"type": "text", "text": "6.1.2 环保DCS系统构建", "text_level": 1, "page_idx": 41}, {"type": "text", "text": "全厂需按照政策要求建设生产设施和治理设施的DCS系统。由企业客户负责协调对应除尘、脱硫脱硝单位按照政策要求自行改造或建设，同时保证历史数据存储5年，并配合易玖生态进行数据采集。", "page_idx": 41}, {"type": "text", "text": "易玖生态从以上19套DCS系统将政策要求的有组织数据采集至环保管控平台，DCS 的权限开放由企业客户协调 DCS 改造厂家进行授权，若需要收取授权费用，由企业客户负责。缺失的信号由企业客户负责从现场仪表或传感器采集传输至DCS系统，现场缺少仪表或传感器的由企业客户负责安装。", "page_idx": 41}, {"type": "text", "text": "6.2 无组织数据采集及接入", "text_level": 1, "page_idx": 41}, {"type": "text", "text": "6.2.1 无组织生产、治理数据采集", "text_level": 1, "page_idx": 41}, {"type": "text", "text": "6.2.1.1无组织生产设备数据信号管理、采集及接入", "text_level": 1, "page_idx": 41}, {"type": "text", "text": "针对无组织排放源，选择具备表征排放源运行状态的皮带、破碎机、振筛、烧结、高炉、转炉、轧机、切割机等生产设备的运行及参数信号进行采集接入入至超低排放管控治一体化平台。", "page_idx": 41}, {"type": "text", "text": "通过对选取的生产数据的采集与接入，实现对与排放源相关的生产设备运行状态及生产负荷的监测管理，对物料存储、物料运输、工艺环节、厂区道路运输等各环节各工艺生产数据进行集中采集；实现生产系统运行状态参数数据能够存储一年以上，并能够随时调阅至少一年内任意时段数据进行查看；生产系统能够与各排放源点位进行关联对应及数据互通；生产系统运行状态数据能够集中展示并进行数据管理及统计分析。", "page_idx": 41}, {"type": "text", "text": "易玖生态负责根据无组织源清单梳理出关联的生产设施采集数据清单。生产设施主要包括输送皮带、给料机、振筛、破碎机等，负责将生产设施的启停开关量、电流等信号通过各分厂生产DCS/PLC系统（企业客户提供以太网口IOPC通讯接口）接入到超低排放管控治一体化平台。", "page_idx": 42}, {"type": "text", "text": "6.2.1.2无组织治理设备数据信号管理、采集及接入", "text_level": 1, "page_idx": 42}, {"type": "text", "text": "烟气处理设备治理数据管理、采集及接入", "text_level": 1, "page_idx": 42}, {"type": "text", "text": "根据核查要求除尘设备需采集并接入除尘器的电流、压差信号。通过对选取的治理数据的接入，实现对与排放源相关的治理设备运行状态及治理负荷的监测管理，采集的信号包括除尘器的启停、设备运行状态、电流、流量、压差等参数。", "page_idx": 42}, {"type": "text", "text": "将无组织排放相关除尘设备运行状态与现有采集的生产数据统一在超低排放管控治一体化平台中集中管理，满足监测监管要求。将采集全厂相关的环保治理设备运行状态参数，统一接入相关治理设备运行参数统一管理，并且需要与全厂无组织排放源进行数据对应与关联。治理设备运行状态参数数据能够存储一年以上，并能够随时调阅至少一年内任意时段数据进行查看。", "page_idx": 42}, {"type": "text", "text": "6.2.1.3无组织生产、治理设备数据采集硬件配置", "text_level": 1, "page_idx": 42}, {"type": "text", "text": "采集硬件负责采集无组织相关的生产、治理数据，并传输至超低排放管控治一体化平台。", "page_idx": 42}, {"type": "text", "text": "6.2.2 无组织抑尘措施数据采集", "text_level": 1, "page_idx": 42}, {"type": "text", "text": "6.2.2.1干雾、雾炮、洗车机等抑尘治理设备数据管理及接入", "text_level": 1, "page_idx": 42}, {"type": "text", "text": "厂内干雾、雾炮、洗车机等治理设备完成建设后，干雾、雾炮设备需采集支管流量信号；洗车机需采集洗车开始、结束时间、洗车时长、水流压力、洗车机车牌识别等。", "page_idx": 42}, {"type": "text", "text": "6.2.3 无组织监测监控数据采集", "text_level": 1, "page_idx": 42}, {"type": "text", "text": "6.2.3.1高清视频监控数据采集并接入", "text_level": 1, "page_idx": 42}, {"type": "text", "text": "对布设的高清视频监控的视频数据进行采集并接入，并统一汇总至无组织排放集中控制系统中，进行统一查询、实时调阅、存储、查询、回放；确保各视频探头、硬盘录像机、大屏工作站在同一网段，网络传输保证千兆带宽；确保高清视频监控具有三个月存储记录，并能够随时调阅至少三个月内任意时段数据进行查看。采集并接入高清视频监控数据。", "page_idx": 42}, {"type": "text", "text": "", "page_idx": 43}, {"type": "text", "text": "6.2.3.2产尘源粉尘浓度监测设备数据采集并接入", "text_level": 1, "page_idx": 43}, {"type": "text", "text": "对布设的产尘源粉尘浓度监测设备的监测数据进行采集并接入，并统一汇总至无组织排放集中控制系统中，进行统一查询、实时调阅、存储、查询；确保产尘源粉尘浓度监测设备的监测数据具有一年存储记录，并能够随时调阅至少一年内任意时段数据进行查看。需采集并接入泵吸式TSP数据。", "page_idx": 43}, {"type": "text", "text": "6.2.3.3空气质量微站数据采集并接入", "text_level": 1, "page_idx": 43}, {"type": "text", "text": "对布设的空气质量微站的监测数据进行采集并接入，并统一汇总至无组织排放集中控制系统中，进行统一查询、实时调阅、存储、查询；确保空气质量微站的监测数据具有一年存储记录，并能够随时调阅至少一年内任意时段数据进行查看。需采集并接入空气质量微站数据。", "page_idx": 43}, {"type": "text", "text": "6.2.3.4物料湿度等级监测仪数据采集并接入", "text_level": 1, "page_idx": 43}, {"type": "text", "text": "对布设的物料湿度等级监测仪的监测数据进行采集并接入，并统一汇总至无组织排放集中控制系统中，进行统一查询、实时调阅、存储、查询；确保物料湿度等级监测仪的监测数据具有一年存储记录，并能够随时调阅至少一年内任意时段数据进行查看。需采集并接入物料湿度等级监测仪数据。", "page_idx": 43}, {"type": "text", "text": "6.2.4 其他数据采集", "text_level": 1, "page_idx": 43}, {"type": "text", "text": "6.2.4.1环保清洁运输车辆定位数据管理、采集及接入", "text_level": 1, "page_idx": 43}, {"type": "text", "text": "根据现场道路运输情况，对厂区内环保车辆定位数据进行接入并集中管理，实时监测车辆工作状态及位置。环保车辆定位数据能够存储一年以上，并能够随时调阅至少一年内任意时段数据进行查看。", "page_idx": 43}, {"type": "text", "text": "6.2.5 无组织产治同步呈现", "text_level": 1, "page_idx": 43}, {"type": "text", "text": "根据超低排放评估监测技术指南，建立完整的无组织排放源清单是企业开展评估监测的基本条件，评估监测过程要重点核查无组织排放源清单的完整性和控制措施符合性。依据无组织排放控制设施运行数据、视频监控数据、颗粒物监测数据等，评估无组织排放控制设施与生产工艺设备同步运转情况。", "page_idx": 43}, {"type": "text", "text": "无组织排放源清单是平台无组织内容呈现内容的前提条件，由企业客户提供规范的无组织源清单，内容和评估监测报告/预评估报告一致。", "page_idx": 43}, {"type": "text", "text": "无组织源清单及其对应治理措施等内容随着企业客户生产系统投用、治理设施完善会持续进行增补完善，而且政策要求平台内容和现场要一致，每次完善易玖生态需要初始化一次，对此双方需共同评估，后续约定两个时间点，集中进行两次更新，超过两次需适当收取人工费。", "page_idx": 43}, {"type": "text", "text": "", "page_idx": 44}, {"type": "text", "text": "以无组织排放源清单梳理要采集的生产和治理设施信号，将其运行信号通过有线或无线传输方式接入到平台。在一体化平台呈现生产设施运行情况、治理设施运行情况、产治同步运行情况并自动形成运行记录。", "page_idx": 44}, {"type": "text", "text": "此项工作作为无组织超低评估监测的重点，信号缺失会造成产治不同步，对验收造成影响，需要双方高度重视，易玖生态负责提供数据清单，企业客户负责完成各项前置条件，才能确保数据正常采集上传平台。", "page_idx": 44}, {"type": "text", "text": "6.3 门禁数据信号采集与接入", "text_level": 1, "page_idx": 44}, {"type": "text", "text": "通过对选取的门禁系统数据的接入，掌握大宗物料通过门禁进出厂区的信息，为无组织排放集中控制系统中门禁系统数据集中管理及分析展示模块的开发奠定基础，确保采集的门禁系统数据具有24个月存储记录，并能够随时调阅至少一年内任意时段数据进行查看。", "page_idx": 44}, {"type": "text", "text": "6.4 计量系统数据接入", "text_level": 1, "page_idx": 44}, {"type": "text", "text": "当企业客户按照《重点行业移动源监管与核查技术指南》完成计量系统升级后即可达到以下表格的数据要求，一体化平台提供计量通知API接口，企业客户协调计量系统厂家负责开发API数据接收接口，当车辆经过计量后，计量系统厂家负责按下表要求构建数据，数据格式为json格式，并在十分钟内推送到超低排放管控治一体化平台API数据接收接口，以便环保平台及时感知到车辆通过的消息，从而获取“计量系统数据”。一体化平台接口返回接收成功标志，计量系统厂家负责留存标志记录一年备查。如计量系统的数据开放和对接涉及费用，由企业客户承担。计量系统与环保平台服务器的网络由易玖生态打通。", "page_idx": 44}, {"type": "text", "text": "7 后期运维方案", "text_level": 1, "page_idx": 44}, {"type": "text", "text": "7.1 TSP、微站、视频监控等在线监测仪器运营方案", "text_level": 1, "page_idx": 44}, {"type": "text", "text": "7.1.1 周监控及平台日常巡检", "text_level": 1, "page_idx": 44}, {"type": "text", "text": "每周远程管控平台巡检，包括升级程序、优化系统、升级补丁、断电后的", "page_idx": 44}, {"type": "text", "text": "数据恢复等。", "page_idx": 45}, {"type": "text", "text": "运维人员每周通过平台查看设备运行状况，发现异常事件可先排查原因，远程解决不了72小时现场解决，24小时电话技术服务", "page_idx": 45}, {"type": "text", "text": "7.1.2 月度巡检", "text_level": 1, "page_idx": 45}, {"type": "text", "text": "每月最少一次对现场进行巡查和维护，每次巡检之后填《月度巡检记录单》并存档保存。", "page_idx": 45}, {"type": "text", "text": "检查设备供电系统是否正常，断路器是否在闭合状态，检测电路有无漏电，接地电阻是否符合要求等。", "page_idx": 45}, {"type": "text", "text": "设备外观检查。外观是否完整，有无破损和遭受撞击的痕迹，航插连接是否松动。", "page_idx": 45}, {"type": "text", "text": "设备气路检查。是否连接正常，气管插接处是否稳固，有无脱落现象。", "page_idx": 45}, {"type": "text", "text": "检查气路的通畅性和密封性，一年更换一次滤筒或管路，采样头及气路保证最少每月清洁一次。", "page_idx": 45}, {"type": "text", "text": "检查设备内部指示灯是否正常指示。", "page_idx": 45}, {"type": "text", "text": "粉尘仪是否正常工作；采集器是否正常工作。", "page_idx": 45}, {"type": "text", "text": "巡检人员发现现场隐患及不规范操作，应及时通知相关负责人，杜绝因现场隐患事件造成设备故障。", "page_idx": 45}, {"type": "text", "text": "扬尘监测仪的耗材更换。", "page_idx": 45}, {"type": "text", "text": "及时检查外接设备（包括LED显示屏）有无破损及异常，擦拭表面灰尘。", "page_idx": 45}, {"type": "text", "text": "扬尘监测仪的定期校准，保证监测设备的数据准确性。", "page_idx": 45}, {"type": "text", "text": "扬尘监测点采用与周边国控站、省控站的数据进行比对校准，具体校准方法为获取需校准站点指定时间段内监测仪的原始监测数据和该时间段内标准站的监测数据并进行筛选，剔除不满足要求的无效数据，将二组数据进行比较、学习、修正，使监测仪的数据按照算法提供的动态系数K不断进行修正达到与标准站一致。部分环境特殊点位辅助质控设备（便携校准仪），共同来完成监测设备的校准。", "page_idx": 45}, {"type": "text", "text": "监测仪现场校准频率为3个月校准一次，特殊情况或监测环境比较恶略的地方可提高校准频率。", "page_idx": 45}, {"type": "text", "text": "7.1.3 应急巡检 ", "text_level": 1, "page_idx": 45}, {"type": "text", "text": "发现设备故障后，及时进场解决异常设备及处理故障，需要返厂维修配件的设备及时更换备件使用。", "page_idx": 46}, {"type": "text", "text": "7.2 平台及管控中心系统运营方案", "text_level": 1, "page_idx": 46}, {"type": "text", "text": "平台运营工作包括服务器运营、数据库备份、平台功能巡检及BUG修复、功能优化升级，以保障平台持续稳定运行。", "page_idx": 46}, {"type": "text", "text": "7.2.1 服务器运营", "text_level": 1, "page_idx": 46}, {"type": "text", "text": "定期（每周）对应用服务器、接口服务器、数据库服务器进行健康巡检，包括检查系统状态、系统备份，主机系统的运行状态，对系统CPU、内存、I/O状态、进程等检查；对系统设置、日志文件进行检查分析，清理系统中的日志文件和垃圾文件，确保服务器有足够冗余量支撑系统运行。如各主要参数超过安全阈值，及时给出升级建议。定期检查各服务器系统基础软件情况，更新补丁，确保服务器处于最佳工作状态。", "page_idx": 46}, {"type": "text", "text": "7.2.2 数据库备份", "text_level": 1, "page_idx": 46}, {"type": "text", "text": "定期（每周）对数据库进行全量备份，每次备份于每周一的凌晨系统使用空闲期进行，数据库备份留存三份，分别存放在不同的物理服务器上，确保故障时能恢复最近的数据。", "page_idx": 46}, {"type": "text", "text": "7.2.3 软件功能巡检", "text_level": 1, "page_idx": 46}, {"type": "text", "text": "平台功能日常巡检，每日对平台各功能板块：有组织、无组织、清洁运输板块进行巡检，检查各功能板块打开是否正常，排查并修复BUG。", "page_idx": 46}, {"type": "text", "text": "7.2.4 功能升级服务", "text_level": 1, "page_idx": 46}, {"type": "text", "text": "在感知层硬件数量不增加前提下提供免费功能优化升级服务。", "page_idx": 46}, {"type": "text", "text": "7.2.5 突发故障处理", "text_level": 1, "page_idx": 46}, {"type": "text", "text": "平台如突发故障（无法打开），易玖生态安排人员第一时间进行排查诊断，如诊断为软件故障第一时间安排人员进行修复，如诊断为服务器、核心交换机、防火墙、显示大屏、硬盘录像机、操作电脑等硬件故障，如需更换硬件的不在运营范围内。", "page_idx": 46}, {"type": "text", "text": "7.2.6 SIM卡充值", "text_level": 1, "page_idx": 46}, {"type": "text", "text": "根据《SIM卡信息登记表》记录SIM卡充值信息及下一次充值时间计划。余额使用完前三天进行下一次充值。", "page_idx": 47}, {"type": "text", "text": "7.3 工作汇报及提交运营表单", "text_level": 1, "page_idx": 47}, {"type": "text", "text": "每月末对本月运营记录进行整理，并在下月初将上月巡检、维修、校准等记录情况以纸质版形式报送至企业客户运营负责人处审核。", "page_idx": 47}, {"type": "text", "text": "7.4 运营服务保障", "text_level": 1, "page_idx": 47}, {"type": "text", "text": "易玖生态已建立专业的维护服务团队，并配备相应的运维车辆及运维工具、材料、备件等。", "page_idx": 47}, {"type": "text", "text": "指定专人负责指定范围内的维护工作，并建立完善的监督制度，每月提交维护和巡检报告。", "page_idx": 47}, {"type": "text", "text": "对出现故障的点位响应时间不超过24小时，一般故障问题的排查及维修不超过72小时，复杂故障问题提前报备的除外。", "page_idx": 47}, {"type": "text", "text": "7.5运营优势 ", "text_level": 1, "page_idx": 47}, {"type": "text", "text": "易玖生态配备运营专车及运营工具，同时设有备品备件库，可做到24小时  \n跟进响应，备品备件齐全可第一时间对现场故障进行处理。为了方便企业客户获得更优质的运营服务体验、监督运营服务的效果，易  \n玖生态提供智慧运营工具，提供一键故障保修、维护助手、迎检助手等功能。", "page_idx": 47}, {"type": "text", "text": "(1) 硬件实力", "text_level": 1, "page_idx": 47}, {"type": "text", "text": "配备专业的便携式比对设备，并建立充足且全面的设备配件库，方便统一调度及管理。公司组建由多专业技能人才构成的售后运营部，并配备相应的运营车辆、运营工具、材料等。指定专人负责指定范围内的维护工作，并建立了完善的运营流程及监督考核制度。", "page_idx": 47}, {"type": "text", "text": "(2) 技术力量", "text_level": 1, "page_idx": 47}, {"type": "text", "text": "环境、机电仪表类专业毕业，有5-15年丰富的环保设备运营经验、技术力量雄厚。运营人员具备中国环境监测总站颁发的环境空气自动监测设备运维与质控资格合格证，拥有两个研发中心，大量高精尖人才多专业协同，提供后台支撑。", "page_idx": 47}, {"type": "text", "text": "(3） 软实力", "text_level": 1, "page_idx": 48}, {"type": "text", "text": "全品牌监测设备及全品类的管控平台运营服务，配件源头直供，让客户享受到最优惠的价格。本地化专家服务团队，2小时内响应，24小时内恢复，让客户获得最及时的服务体验。运维方案选择多样化：根据运维范围、巡检频率、故障配件计划采购、环保检查压力及达到效果等情况，客户可据实选择更适用自己的方案。易玖生态对超低排放改造有过长时间驻场服务经验，全程伴随企业，具备丰富的现场迎检经验，助力企业通过超低排放验收和评上AB级。目前在运维的钢铁焦化水泥超低平台项目有近20个。规范化运营管理，确保设备正常运行的同时规范建立运行、维护记录等台账资料，规避环保检查风险。与设备厂商深度合作，共同开发校准软件系统，提供检测故障的软件工具等，避免返厂维修，节约故障处理时长。", "page_idx": 48}, {"type": "text", "text": "7.6 易玖生态提供的智慧运营信息化工具", "text_level": 1, "page_idx": 48}, {"type": "text", "text": "为了方便企业客户获得更优质的运营服务体验、监督运营服务的效果，易玖生态提供智慧运营工具，提供一键故障保修、维护助手、迎检助手等功能。", "page_idx": 48}, {"type": "text", "text": "环保易运维 :。 10:39 HD 图 10:39 HD 图  \n运维管家 + < 选择设备 ： 。 < 维修助手 ： 。  \n常用功能 > 有限责任公司& QTSP监测 你好，欢迎使用维修助手！一键报修 维修助手 设备总览 迎检助手00 选择客户新泰 >  \n2022-10运营周报生生成，请查收 Q 0 Q请输入故障描述  \n运营看板 0 8 推荐问题供应商服务分析月202207 处理速度 服务排名： TSP监测-1#TSP 1 TSP监鲁-高线上般数据  \n15 质保期北京易玖生态环境有限公司  \n12 供货近30天|维修6次 3环保平台-数据显示都为“”  \n9 负责人|炼铁厂丁欣梅 一键报修 4环保平台-地图报错  \n630 OTSP监测-2#TSP-门口TSP监 5CEMS监测-数据频繁超标先和环保北京万维智易时代清环宜境供应商1 测门口卡口监控搜索记录非质保期北京易玖生态环境有限公CEMS达标情况汇总 司供货近30天|维修15次■达标 ■超标 ■未上报 ■异常修约 负责人|炼铁厂丁欣梅 一键报修 TSP数值偏低异常修约 TSP设备没办法联网30达标 TSP监测-3#TSP-北门 TSP设备监测数据超低200 200 日常监控达标 运维期天津智意时代运维近30天| TSP设备一直报警上报 超标 维修15练厂丁放构 一键报修 TSP设备监测数据忽高忽低", "page_idx": 48}, {"type": "text", "text": "7.7 配置专业的运营服务小组", "text_level": 1, "page_idx": 49}, {"type": "text", "text": "易玖生态建立有专业的维护服务团队，售后工程师均具备环境空气类自动监测设备运维与质控培训合格上岗证。", "page_idx": 49}]