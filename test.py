
# client = OpenAI(
#     api_key='sk-282f3112bd714d6e85540da173b5517c',
#     base_url='https://dashscope.aliyuncs.com/compatible-mode/v1',
# )
from openai import OpenAI
client = OpenAI(
    api_key='9a63bfb2-d9d2-4cc6-99d5-e8ff010e3eaf',
    base_url='http://localhost:8081/v1',
)
# model_type = 'qwen-plus-2025-01-25'
model_type = 'gpt-4o'
print(f'model_type: {model_type}')

messages = []
for query in ['你是谁？', "what's your name?", '你是谁研发的？']:
    messages.append({
        'role': 'user',
        'content': query
    })
    resp = client.chat.completions.create(
        model=model_type,
        messages=messages,
        seed=42)
    response = resp.choices[0].message.content
    print(f'query: {query}')
    print(f'response: {response}')
    messages.append({'role': 'assistant', 'content': response})

# 流式
for query in ['78654+657=?', '晚上睡不着觉怎么办']:
    messages.append({'role': 'user', 'content': query})
    stream_resp = client.chat.completions.create(
        model=model_type,
        messages=messages,
        stream=True,
        seed=42)
    print(f'query: {query}')
    print('response: ', end='')
    response = ''
    for chunk in stream_resp:
        response += chunk.choices[0].delta.content
        print(chunk.choices[0].delta.content, end='', flush=True)
    print()
    messages.append({'role': 'assistant', 'content': response})