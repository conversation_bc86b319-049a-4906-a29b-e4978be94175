# ============================================================================ #
#                                      客户端                                     #
# ============================================================================ #
import aiofiles

# 导入 FastMCP 库，用于创建 Model Context Protocol (MCP) 服务器
from fastmcp import FastMCP,Context
from fastmcp.prompts.prompt import Message, PromptMessage, TextContent

# 创建一个 FastMCP 服务器实例，命名为 "My MCP Server"
# MCP 是一种协议，允许 AI 助手与外部工具和数据源进行交互
mcp = FastMCP("My MCP Server")


# 使用 @mcp.tool 装饰器将函数注册为 MCP 工具

@mcp.tool()
async def add_numbers(a: int, b: int, ctx: Context) -> int:
    """
    加法工具函数
    
    参数:
        a (int): 第一个加数
        b (int): 第二个加数
        ctx (Context): 请求上下文，包含 request_id 等信息
    返回:
        int: 两个数的和
    """
    await ctx.info(f"调用 add_numbers 工具: {a} + {b}")
    return a + b

@mcp.tool()
async def multiply_numbers(a: int, b: int, ctx: Context) -> int:
    """
    乘法工具函数
    
    参数:
        a (int): 第一个乘数
        b (int): 第二个乘数
        ctx (Context): 请求上下文，包含 request_id 等信息
    返回:
        int: 两个数的积
    """
    await ctx.info(f"调用 multiply_numbers 工具: {a} * {b}")
    return a * b
# 该函数可被 AI 助手调用，实现问候功能
@mcp.tool()
async def greet(name: str, ctx: Context) -> str:
    """
    问候工具函数
    
    参数:
        name (str): 要问候的人的姓名
    返回:
        str: 包含问候语的字符串
    """
    await ctx.info("调用 greet 工具")
    return f"Hello, {name}!"


# 注册产品搜索工具，可选分类过滤
@mcp.tool(
    name="find_products",           # LLM 工具名称
    description="Search the product catalog with optional category filtering.", # 工具描述
    tags={"catalog", "search"},      # 工具标签，便于组织/筛选
)
def search_products_implementation(query: str, category: str | None = None) -> list[dict]:
    """
    产品搜索实现函数
    参数:
        query (str): 搜索关键词
        category (str | None): 可选分类
    返回:
        list[dict]: 产品列表
    """
    # 实际实现可替换为数据库或 API 查询
    print(f"Searching for '{query}' in category '{category}'")
    return [{"id": 2, "name": "Another Product"}]



# 注册动态资源，返回字符串问候语
@mcp.resource("resource://greeting")
def get_greeting() -> str:
    """
    提供简单问候消息的资源
    返回:
        str: 问候语
    """
    return "Hello from FastMCP Resources!"


# 注册资源，返回 JSON 格式的应用配置
@mcp.resource("data://config")
def get_config() -> dict:
    """
    提供应用配置的资源，自动序列化为 JSON
    返回:
        dict: 配置信息
    """
    return {
        "theme": "dark",
        "version": "1.2.0",
        "features": ["tools", "resources"],
    }
    
# --------------------------- Async Resources  异步资源 -------------------------- #

# 如果 fastmcp.Context 可用，则导入 Context
try:
    from fastmcp import Context
except ImportError:
    # 定义一个简单的 Context 占位类以避免错误
    class Context:
        def __init__(self, request_id=None):
            self.request_id = request_id

# 注册异步资源，返回系统状态信息
@mcp.resource("resource://system-status")
async def get_system_status(ctx: Context) -> dict:
    """
    提供系统状态信息的资源
    参数:
        ctx (Context): 请求上下文,包含 request_id 等信息
    返回:
        dict: 系统状态及请求 ID
    """
    await ctx.info("调用资源 system-status")
    return {
        "status": "operational",
        "request_id": ctx.request_id
    }


# 注册异步资源，返回指定名称的详细信息
@mcp.resource("resource://{name}/details")
async def get_details(name: str, ctx: Context) -> dict:
    """
    获取指定名称的详细信息资源
    参数:
        name (str): 资源名称
        ctx (Context): 请求上下文，包含 request_id 等信息
    返回:
        dict: 资源详情及访问时间（以 request_id 标识）
    """
    return {
        "name": name,
        "accessed_at": ctx.request_id
    }
    
@mcp.resource("file:///app/data/important_log.txt", mime_type="text/plain")
async def read_important_log() -> str:
    """Reads content from a specific log file asynchronously."""
    try:
        async with aiofiles.open("/app/data/important_log.txt", mode="r") as f:
            content = await f.read()
        return content
    except FileNotFoundError:
        return "Log file not found."
    
'''
在 FastMCP 生态系统中，这一行可能是不必要的。
然而，包含它可以确保你的 FastMCP 服务器以一致的方式为所有用户和客户端运行，因此被推荐为最佳实践。
'''

# ------------------------------------ 提示词 ----------------------------------- #
# Basic prompt returning a string (converted to user message automatically)
@mcp.prompt
def ask_about_topic(topic: str) -> str:
    """Generates a user message asking for an explanation of a topic."""
    return f"Can you please explain the concept of '{topic}'?"

# Prompt returning a specific message type
@mcp.prompt
def generate_code_request(language: str, task_description: str) -> PromptMessage:
    """Generates a user message requesting code generation."""
    content = f"Write a {language} function that performs the following task: {task_description}"
    return PromptMessage(role="user", content=TextContent(type="text", text=content))



# 主程序入口
# 只有当该文件被直接运行时（而不是被导入时）才会执行以下代码
if __name__ == "__main__":
    # 启动 MCP 服务器，监听来自 AI 助手的连接和工具调用请求
    # transport="http" 表示使用 HTTP 协议，host="0.0.0.0" 监听所有地址，port=9000 指定端口
    # log_level="debug" 开启调试日志，path="/my-custom-sse-path" 指定自定义 SSE 路径
    mcp.run(transport="http", host="0.0.0.0", port=9002, log_level="debug", path="/my-custom-sse-path")

'''
要让 FastMCP 为我们运行服务器，可以使用 fastmcp run 命令。
这将启动服务器并使其保持运行状态，直到被停止。
默认情况下，它将使用 stdio 传输协议，这是一种用于与服务器交互的简单文本协议。
示例：
fastmcp run my_server.py:mcp

# 默认以 STDIO 传输运行服务器
mcp.run()

# 使用其他传输方式，例如 Streamable HTTP：
# mcp.run(transport="http", host="127.0.0.1", port=9000)
'''