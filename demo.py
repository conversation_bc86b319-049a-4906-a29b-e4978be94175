# Copyright (c) Alibaba, Inc. and its affiliates.

from ms_agent.llm.openai import OpenAIChat
from ms_agent.tools.exa import ExaSearch
from ms_agent.workflow.principle import MECEPrinciple
from ms_agent.workflow.research_workflow import ResearchWorkflow


def run_workflow(user_prompt: str, task_dir: str, reuse: bool,
                 chat_client: OpenAIChat, search_engine: ExaSearch):

    research_workflow = ResearchWorkflow(
        client=chat_client,
        principle=MECEPrinciple(),
        search_engine=search_engine,
        workdir=task_dir,
        reuse=reuse,
    )

    research_workflow.run(user_prompt=user_prompt)


if __name__ == '__main__':

    query: str = '在计算化学这个领域，我们通常使用Gaussian软件模拟各种情况下分子的结构和性质计算，比如在关键词中加入\'field=x+100\'代表了在x方向增加了电场。但是，当体系是经典的单原子催化剂时，它属于分子催化剂，在反应环境中分子的朝向是不确定的，那么理论模拟的x方向电场和实际电场是不一致的。请问：通常情况下，理论计算是如何模拟外加电场存在的情况？'  # noqa
    task_workdir: str = './research_output'
    reuse: bool = False

    # Get chat client OpenAI compatible api
    chat_client = OpenAIChat(
        api_key='sk-282f3112bd714d6e85540da173b5517c',
        base_url='https://dashscope.aliyuncs.com/compatible-mode/v1',
        model='qwen-plus-2025-01-25',
    )

    # Get web-search engine client
    # For the ExaSearch, you can get your API key from https://exa.ai
    exa_search = ExaSearch(api_key='47da014c-f216-43b7-90ed-a8acd038231f')

    run_workflow(
        user_prompt=query,
        task_dir=task_workdir,
        reuse=reuse,
        chat_client=chat_client,
        search_engine=exa_search,
    )
