# XiYan MCP 服务器异步数据库升级

本次升级将 XiYan MCP 服务器的 SQLAlchemy 从同步版本升级为异步版本，以提高高并发条件下的性能。

## 主要改进

### 1. 异步数据库连接
- 支持异步 MySQL (`aiomysql`)
- 支持异步 PostgreSQL (`asyncpg`) 
- 支持异步 SQLite (`aiosqlite`)
- 连接池配置优化，提高连接复用率

### 2. 连接池配置
```python
# MySQL/PostgreSQL 配置
pool_size=20,          # 基础连接池大小
max_overflow=30,       # 最大溢出连接数
pool_timeout=30,       # 获取连接超时时间（秒）
pool_recycle=3600,     # 连接回收时间（秒）
pool_pre_ping=True,    # 连接前ping检测
```

### 3. 查询缓存
- 基于 MD5 的查询缓存系统
- 可配置的缓存大小和 TTL
- 自动过期清理机制
- 减少重复查询的数据库负载

### 4. 并发控制
- 信号量限制最大并发查询数 (默认100)
- 防止数据库过载
- 保证系统稳定性

### 5. 性能优化配置
```python
PERFORMANCE_CONFIG = {
    'max_concurrent_queries': 100,   # 最大并发查询数
    'result_cache_size': 1000,       # 结果缓存大小
    'cache_ttl': 300,                # 缓存TTL（秒）
    'enable_query_cache': True,      # 启用查询缓存
}
```

## 新增文件

### 1. `utils/async_db_source.py`
异步版本的数据库访问类，提供：
- 异步查询执行
- 查询结果缓存
- 并发控制
- 连接池管理

### 2. `async_database_env.py`
异步数据库环境类，管理异步数据库实例的生命周期。

### 3. `utils/async_config.py`
异步操作和性能优化的配置文件。

### 4. `utils/query_cache.py`
查询缓存实现，提供高效的查询结果缓存机制。

## 兼容性

- 保留原有同步版本的接口
- 新增异步版本的并行实现
- 主要工具函数 `get_data` 现在使用异步版本
- 向后兼容现有配置

## 性能提升

### 1. 并发处理
- 同步版本：单线程阻塞执行
- 异步版本：支持高并发非阻塞执行

### 2. 连接管理
- 连接池复用，减少连接建立开销
- 智能连接回收，避免连接泄漏

### 3. 查询优化
- 查询结果缓存，减少重复查询
- 并发控制，防止数据库过载

### 4. 资源管理
- 优雅关闭，确保资源清理
- 异步上下文管理

## 部署注意事项

### 1. 依赖更新
确保安装以下新依赖：
```bash
pip install sqlalchemy[asyncio] asyncpg aiomysql aiosqlite
```

### 2. 配置调整
可根据实际负载调整 `async_config.py` 中的配置参数：
- 连接池大小
- 并发查询限制
- 缓存配置

### 3. 监控指标
建议监控以下指标：
- 数据库连接池使用率
- 查询缓存命中率
- 并发查询数量
- 响应时间

## 预期性能提升

在高并发场景下，预期可以获得：
- **3-5倍** 的并发处理能力提升
- **30-50%** 的响应时间减少（缓存命中时）
- **显著减少** 数据库连接开销
- **更好的** 系统稳定性和可扩展性

## 使用建议

1. **生产环境部署前**，建议在测试环境进行压力测试
2. **根据实际负载**调整连接池和缓存配置
3. **监控系统资源**使用情况，及时调整参数
4. **定期清理缓存**，避免内存占用过高
