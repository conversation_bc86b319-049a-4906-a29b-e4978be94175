#!/usr/bin/env python3
"""
性能测试脚本 - 比较同步和异步版本的性能
"""

import asyncio
import time
import statistics
import sys
import os
from concurrent.futures import ThreadPoolExecutor
from typing import List

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from xiyan_mcp_server.utils.db_config import DBConfig
from xiyan_mcp_server.utils.db_util import init_db_conn, init_async_db_conn
from xiyan_mcp_server.utils.db_source import HITLSQLDatabase
from xiyan_mcp_server.utils.async_db_source import AsyncHITLSQLDatabase
from xiyan_mcp_server.database_env import DataBaseEnv
from xiyan_mcp_server.async_database_env import AsyncDataBaseEnv


class PerformanceTest:
    def __init__(self, db_config: DBConfig):
        self.db_config = db_config
        self.test_queries = [
            "SELECT COUNT(*) FROM test_table",
            "SELECT name FROM test_table LIMIT 5", 
            "SELECT id, value FROM test_table WHERE value > 50 LIMIT 10",
            "SELECT AVG(value) FROM test_table",
            "SELECT MAX(id) FROM test_table"
        ]

    def sync_test_single_query(self, query: str) -> float:
        """测试单个同步查询的性能"""
        start_time = time.time()
        
        db_engine = init_db_conn(self.db_config)
        db_source = HITLSQLDatabase(db_engine)
        db_env = DataBaseEnv(db_source)
        
        success, result = db_env.database.fetch(query)
        end_time = time.time()
        
        return end_time - start_time

    async def async_test_single_query(self, query: str) -> float:
        """测试单个异步查询的性能"""
        start_time = time.time()
        
        async_engine = init_async_db_conn(self.db_config)
        async_db_source = AsyncHITLSQLDatabase(async_engine)
        async_db_env = AsyncDataBaseEnv(async_db_source)
        
        await async_db_env.initialize()
        success, result = await async_db_env.database.fetch(query)
        
        await async_engine.dispose()
        end_time = time.time()
        
        return end_time - start_time

    def sync_concurrent_test(self, num_threads: int = 10, queries_per_thread: int = 5) -> List[float]:
        """同步并发测试"""
        results = []
        
        def worker():
            times = []
            for query in self.test_queries[:queries_per_thread]:
                duration = self.sync_test_single_query(query)
                times.append(duration)
            return times
        
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(worker) for _ in range(num_threads)]
            for future in futures:
                results.extend(future.result())
        
        total_time = time.time() - start_time
        results.append(total_time)  # 总时间
        return results

    async def async_concurrent_test(self, num_concurrent: int = 10, queries_per_task: int = 5) -> List[float]:
        """异步并发测试"""
        async def worker():
            times = []
            async_engine = init_async_db_conn(self.db_config)
            async_db_source = AsyncHITLSQLDatabase(async_engine)
            async_db_env = AsyncDataBaseEnv(async_db_source)
            await async_db_env.initialize()
            
            for query in self.test_queries[:queries_per_task]:
                start_time = time.time()
                success, result = await async_db_env.database.fetch(query)
                duration = time.time() - start_time
                times.append(duration)
            
            await async_engine.dispose()
            return times
        
        start_time = time.time()
        tasks = [worker() for _ in range(num_concurrent)]
        results_list = await asyncio.gather(*tasks)
        total_time = time.time() - start_time
        
        results = []
        for worker_results in results_list:
            results.extend(worker_results)
        results.append(total_time)  # 总时间
        return results

    async def run_performance_comparison(self):
        """运行性能对比测试"""
        print("🚀 XiYan MCP 服务器性能测试")
        print("=" * 50)
        
        # 单查询测试
        print("\n📊 单查询性能测试")
        print("-" * 30)
        
        sync_times = []
        async_times = []
        
        for i, query in enumerate(self.test_queries):
            print(f"\n测试查询 {i+1}: {query[:50]}...")
            
            # 同步测试
            sync_time = self.sync_test_single_query(query)
            sync_times.append(sync_time)
            print(f"  同步版本: {sync_time:.4f}s")
            
            # 异步测试
            async_time = await self.async_test_single_query(query)
            async_times.append(async_time)
            print(f"  异步版本: {async_time:.4f}s")
            
            improvement = ((sync_time - async_time) / sync_time) * 100
            print(f"  性能提升: {improvement:.1f}%")
        
        # 并发测试
        print("\n🔥 并发性能测试")
        print("-" * 30)
        
        concurrent_levels = [5, 10, 20]
        
        for level in concurrent_levels:
            print(f"\n并发级别: {level}")
            
            # 同步并发测试
            print("  运行同步并发测试...")
            sync_concurrent_results = self.sync_concurrent_test(level, 3)
            sync_total_time = sync_concurrent_results[-1]
            sync_avg_time = statistics.mean(sync_concurrent_results[:-1])
            print(f"  同步版本 - 总时间: {sync_total_time:.4f}s, 平均查询时间: {sync_avg_time:.4f}s")
            
            # 异步并发测试
            print("  运行异步并发测试...")
            async_concurrent_results = await self.async_concurrent_test(level, 3)
            async_total_time = async_concurrent_results[-1]
            async_avg_time = statistics.mean(async_concurrent_results[:-1])
            print(f"  异步版本 - 总时间: {async_total_time:.4f}s, 平均查询时间: {async_avg_time:.4f}s")
            
            total_improvement = ((sync_total_time - async_total_time) / sync_total_time) * 100
            avg_improvement = ((sync_avg_time - async_avg_time) / sync_avg_time) * 100
            
            print(f"  总时间提升: {total_improvement:.1f}%")
            print(f"  平均时间提升: {avg_improvement:.1f}%")
        
        # 总结
        print("\n📈 性能测试总结")
        print("=" * 50)
        
        overall_sync_avg = statistics.mean(sync_times)
        overall_async_avg = statistics.mean(async_times)
        overall_improvement = ((overall_sync_avg - overall_async_avg) / overall_sync_avg) * 100
        
        print(f"单查询平均性能提升: {overall_improvement:.1f}%")
        print("\n🎯 推荐配置:")
        print("- 高并发场景建议使用异步版本")
        print("- 根据实际负载调整连接池大小")
        print("- 启用查询缓存以获得更好性能")
        print("- 监控系统资源使用情况")


async def main():
    """主测试函数"""
    # 使用SQLite进行演示测试 - 请根据实际环境修改
    import sqlite3
    test_db_path = "/tmp/perf_test.db"
    
    # 创建测试数据库和表
    conn = sqlite3.connect(test_db_path)
    cursor = conn.cursor()
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS test_table (
            id INTEGER PRIMARY KEY,
            name TEXT,
            value INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # 插入一些测试数据
    for i in range(100):
        cursor.execute("INSERT OR REPLACE INTO test_table (id, name, value) VALUES (?, ?, ?)", 
                      (i, f'test_{i}', i * 10))
    conn.commit()
    conn.close()
    
    db_config = DBConfig(
        dialect='sqlite',
        db_path=test_db_path
    )
    
    try:
        test_runner = PerformanceTest(db_config)
        await test_runner.run_performance_comparison()
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        print("请检查数据库连接配置和依赖安装")
    finally:
        # 清理测试数据库
        if os.path.exists(test_db_path):
            os.remove(test_db_path)


if __name__ == "__main__":
    print("开始性能测试...")
    print("注意：请确保已安装所需依赖并配置正确的数据库连接")
    
    # 运行测试
    asyncio.run(main())
