# Smithery.ai configuration
startCommand:
  type: stdio
  configSchema:
    # JSO<PERSON> Schema defining the configuration options for the MCP.
    {
      "YML":"src/xiyan_mcp_server/config_demo.yml"
    }
  commandFunction:
    # A function that produces the CLI command to start the MCP on stdio.
    |-
    (config) => ({
      "command": "python",
      "args": [
        "-m",
        "xiyan_mcp_server"
      ]
    })
