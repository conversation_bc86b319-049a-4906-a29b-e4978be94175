

from flask import Flask, request, jsonify
from modelscope import AutoModelForCausalLM, AutoTokenizer
import torch  # require torch==2.2.2,accelerate>=0.26.0,numpy=2.2.3,modelscope

# XiYan SQL生成的提示模板
nl2sqlite_template_cn = """你是一名{dialect}专家，现在需要阅读并理解下面的【数据库schema】描述，以及可能用到的【参考信息】，并运用{dialect}知识生成sql语句回答【用户问题】。
【用户问题】
{question}

【数据库schema】
{db_schema}

【参考信息】
{evidence}

【用户问题】
{question}

```sql"""

model_name = "/home/<USER>/llm_project/llm_model/XGenerationLab/XiYanSQL-QwenCoder-14B-2504"
local_model = AutoModelForCausalLM.from_pretrained(
    model_name, device_map="cuda", torch_dtype=torch.float16
)
local_tokenizer = AutoTokenizer.from_pretrained(model_name)
app = Flask(__name__)


@app.route("/chat/completions", methods=["POST"])
def chat_completions():
    # 获取请求中的数据
    input_data = request.json

    # 提取提示（prompt）
    messages = input_data.get("messages", [])

    if not messages:
        return jsonify({"error": "No messages provided"})

    # 检查是否为SQL生成请求
    sql_params = input_data.get("sql_params")
    if sql_params:
        # 使用结构化的SQL生成模板
        dialect = sql_params.get("dialect", "SQLite")
        db_schema = sql_params.get("db_schema", "")
        question = sql_params.get("question", "")
        evidence = sql_params.get("evidence", "")
        
        prompt = nl2sqlite_template_cn.format(
            dialect=dialect,
            db_schema=db_schema,
            question=question,
            evidence=evidence
        )
        message = [{'role': 'user', 'content': prompt}]
    else:
        # 使用原始messages
        message = messages

    text = local_tokenizer.apply_chat_template(
        message, tokenize=False, add_generation_prompt=True
    )
    inputs = local_tokenizer([text], return_tensors="pt")

    # 将输入张量移动到与模型相同的设备上
    inputs = {k: v.to(local_model.device) for k, v in inputs.items()}

    # 编码输入并生成响应
    generated_ids = local_model.generate(
        inputs["input_ids"],
        max_new_tokens=1024,
        temperature=0.1,
        top_p=0.8,
        do_sample=True,
    )

    generated_ids = [
        output_ids[len(input_ids) :]
        for input_ids, output_ids in zip(inputs["input_ids"], generated_ids)
    ]
    generated_text = local_tokenizer.batch_decode(
        generated_ids, skip_special_tokens=True
    )[0]
    

    # 生成响应格式
    response = {
        "id": "xiyan",
        "object": "chat.completion",
        "created": 1234567890,
        "model": model_name,
        "choices": [
            {
                "index": 0,
                "message": {"content": generated_text},
                "finish_reason": "length",
            }
        ],
    }
    print(generated_text)
    return jsonify(response)


if __name__ == "__main__":
    # this flask server runs on http://localhost:5090
    app.run(host="0.0.0.0", port=5090)
