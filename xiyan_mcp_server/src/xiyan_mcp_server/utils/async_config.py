# 异步数据库连接池配置
ASYNC_DB_POOL_CONFIG = {
    'mysql': {
        'pool_size': 20,          # 连接池大小
        'max_overflow': 30,       # 最大溢出连接数
        'pool_timeout': 30,       # 获取连接超时时间（秒）
        'pool_recycle': 3600,     # 连接回收时间（秒）
        'pool_pre_ping': True,    # 连接前ping检测
    },
    'postgresql': {
        'pool_size': 20,
        'max_overflow': 30,
        'pool_timeout': 30,
        'pool_recycle': 3600,
        'pool_pre_ping': True,
    },
    'sqlite': {
        'pool_size': 5,           # SQLite不需要太多连接
        'max_overflow': 10,
        'pool_timeout': 30,
        'pool_recycle': 3600,
        'pool_pre_ping': True,
    }
}

# 异步操作配置
ASYNC_CONFIG = {
    'connection_timeout': 30,     # 连接超时
    'query_timeout': 60,          # 查询超时
    'max_retries': 3,             # 最大重试次数
    'retry_delay': 1,             # 重试延迟（秒）
}

# 性能优化配置
PERFORMANCE_CONFIG = {
    'max_concurrent_queries': 100,   # 最大并发查询数
    'result_cache_size': 1000,       # 结果缓存大小
    'cache_ttl': 300,                # 缓存TTL（秒）
    'enable_query_cache': True,      # 启用查询缓存
}
