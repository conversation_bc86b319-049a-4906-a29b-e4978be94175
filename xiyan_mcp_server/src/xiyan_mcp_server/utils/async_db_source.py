import asyncio
from typing import Any, Dict, List, Optional, Tuple

from sqlalchemy import MetaData, Table, select, text
from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession
from sqlalchemy.orm import sessionmaker

from .db_mschema import MSchema
from .db_util import examples_to_str, preprocess_sql_query
from .query_cache import query_cache
from .async_config import PERFORMANCE_CONFIG


class AsyncHITLSQLDatabase:
    """异步版本的HITLSQLDatabase类，用于高并发场景"""
    
    def __init__(self, engine: AsyncEngine, schema: Optional[str] = None, metadata: Optional[MetaData] = None,
                 ignore_tables: Optional[List[str]] = None, include_tables: Optional[List[str]] = None,
                 sample_rows_in_table_info: int = 3, indexes_in_table_info: bool = False,
                 custom_table_info: Optional[dict] = None, view_support: bool = False, max_string_length: int = 300,
                 mschema: Optional[MSchema] = None, db_name: Optional[str] = ''):
        
        self._engine = engine
        self._schema = schema
        self._metadata = metadata or MetaData()
        self._ignore_tables = ignore_tables or []
        self._include_tables = include_tables
        self._sample_rows_in_table_info = sample_rows_in_table_info
        self._indexes_in_table_info = indexes_in_table_info
        self._custom_table_info = custom_table_info or {}
        self._view_support = view_support
        self._max_string_length = max_string_length
        self._db_name = db_name
        self._dialect = engine.dialect.name
        
        # 创建异步session工厂
        self._async_session = sessionmaker(
            self._engine, class_=AsyncSession, expire_on_commit=False
        )
        
        # 创建并发控制信号量
        self._semaphore = asyncio.Semaphore(PERFORMANCE_CONFIG['max_concurrent_queries'])
        
        if mschema is not None:
            self._mschema = mschema
        else:
            self._mschema = MSchema(db_id=db_name, schema=schema)
            # 注意：异步初始化需要在异步上下文中调用
            self._initialized = False

    @property
    def mschema(self) -> MSchema:
        """Return M-Schema"""
        return self._mschema

    @property
    def db_name(self) -> str:
        """Return db_name"""
        return self._db_name

    @property
    def dialect(self) -> str:
        """Return dialect"""
        return self._dialect

    async def init_metadata(self):
        """异步初始化元数据"""
        if not self._initialized:
            async with self._engine.begin() as conn:
                await conn.run_sync(self._metadata.reflect)
            
            # 获取可用表列表
            self._usable_tables = []
            for table_name in self._metadata.tables.keys():
                if self._include_tables is None or table_name in self._include_tables:
                    if table_name not in self._ignore_tables:
                        self._usable_tables.append(table_name)
            
            if not self._mschema.tables:
                await self.init_mschema()
            self._initialized = True

    async def init_mschema(self):
        """异步初始化MSchema"""
        for table_name in self._usable_tables:
            # 获取表信息 - 修正这里的实现
            table = self._metadata.tables[table_name]
            
            # 添加表到mschema
            self._mschema.add_table(table_name, fields={}, comment="")
            
            # 处理每个字段
            for column in table.columns:
                field_type = f"{column.type!s}"
                primary_key = column.primary_key
                nullable = column.nullable
                default = column.default.arg if column.default else None
                
                # 获取示例数据
                try:
                    examples = await self.fetch_distinct_values(table_name, column.name, 5)
                except:
                    examples = []
                
                examples = examples_to_str(examples)
                
                self._mschema.add_field(
                    table_name, column.name, field_type=field_type,
                    primary_key=primary_key, nullable=nullable, default=default,
                    autoincrement=column.autoincrement if hasattr(column, 'autoincrement') else False,
                    comment="", examples=examples
                )

    async def fetch_distinct_values(self, table_name: str, column_name: str, max_num: int = 5):
        """异步获取字段的不同值"""
        table = self._metadata.tables[table_name]
        query = select(table.c[column_name]).distinct().limit(max_num)
        
        values = []
        async with self._engine.begin() as conn:
            result = await conn.execute(query)
            distinct_values = result.fetchall()
            for value in distinct_values:
                if value[0] is not None and value[0] != '':
                    values.append(value[0])
        return values

    async def fetch(self, sql_query: str) -> Tuple[bool, Any]:
        """异步执行SQL查询并返回结果（支持缓存和并发控制）"""
        sql_query = preprocess_sql_query(sql_query)
        
        # 尝试从缓存获取结果
        cached_result = await query_cache.get(sql_query)
        if cached_result is not None:
            return cached_result
        
        # 使用信号量控制并发
        async with self._semaphore:
            async with self._engine.begin() as conn:
                try:
                    result = await conn.execute(text(sql_query))
                    records = result.fetchall()
                    records = [tuple(row) for row in records]
                    success_result = (True, records)
                    
                    # 缓存成功的结果
                    await query_cache.set(sql_query, success_result)
                    return success_result
                except Exception as e:
                    error_result = (False, str(e))
                    return error_result

    async def fetch_with_column_name(self, sql_query: str) -> Tuple[Any, List[str]]:
        """异步执行SQL查询并返回结果和列名"""
        sql_query = preprocess_sql_query(sql_query)
        
        async with self._semaphore:
            async with self._engine.begin() as conn:
                try:
                    result = await conn.execute(text(sql_query))
                    columns = list(result.keys())
                    records = result.fetchall()
                    return records, columns
                except Exception as e:
                    return None, []

    async def fetch_with_error_info(self, sql_query: str) -> Tuple[Any, str]:
        """异步执行SQL查询并返回错误信息"""
        info = ''
        sql_query = preprocess_sql_query(sql_query)
        
        async with self._semaphore:
            async with self._engine.begin() as conn:
                try:
                    result = await conn.execute(text(sql_query))
                    records = result.fetchall()
                    return records, info
                except Exception as e:
                    return None, str(e)

    async def fetch_truncated(self, sql_query: str, max_rows: Optional[int] = None, max_str_len: int = 30) -> Dict:
        """异步执行SQL查询并返回截断的结果"""
        sql_query = preprocess_sql_query(sql_query)
        
        async with self._semaphore:
            async with self._engine.begin() as conn:
                try:
                    result = await conn.execute(text(sql_query))
                    records = result.fetchall()
                    truncated_results = []
                    
                    if max_rows:
                        records = records[:max_rows]
                    
                    for row in records:
                        truncated_row = tuple(
                            self.truncate_word(column, length=max_str_len)
                            for column in row
                        )
                        truncated_results.append(truncated_row)
                    
                    return {"truncated_results": truncated_results, "fields": list(result.keys())}
                except Exception as e:
                    return {"truncated_results": str(e), "fields": []}

    def truncate_word(self, content: Any, *, length: int) -> str:
        """截断字符串内容"""
        if content is None:
            return "None"
        
        str_content = str(content)
        if len(str_content) <= length:
            return str_content
        else:
            return str_content[:length] + "..."

    def trunc_result_to_markdown(self, sql_res: Dict) -> str:
        """将数据库查询结果转换成markdown格式"""
        truncated_results = sql_res.get("truncated_results", [])
        fields = sql_res.get("fields", [])
        
        if isinstance(truncated_results, str):
            return f"Error: {truncated_results}"
        
        if not truncated_results or not fields:
            return "No results found."
        
        # 创建markdown表格
        markdown_lines = []
        
        # 表头
        header = "| " + " | ".join(fields) + " |"
        markdown_lines.append(header)
        
        # 分隔线
        separator = "| " + " | ".join(["---"] * len(fields)) + " |"
        markdown_lines.append(separator)
        
        # 数据行
        for row in truncated_results:
            row_str = "| " + " | ".join(str(cell) for cell in row) + " |"
            markdown_lines.append(row_str)
        
        return "\n".join(markdown_lines)

    async def execute(self, sql_query: str, timeout=5) -> Any:
        """异步执行SQL语句"""
        sql_query = preprocess_sql_query(sql_query)
        
        async with self._semaphore:
            async with self._engine.begin() as conn:
                try:
                    await conn.execute(text(sql_query))
                    return True
                except Exception as e:
                    print("SQL执行异常：", str(e))
                    return None

    async def close(self):
        """关闭异步连接"""
        await self._engine.dispose()
