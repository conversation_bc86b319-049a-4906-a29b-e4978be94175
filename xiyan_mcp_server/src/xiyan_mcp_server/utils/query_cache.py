import asyncio
import hashlib
import time
from typing import Any, Dict, Optional, Tuple
from .async_config import PERFORMANCE_CONFIG


class AsyncQueryCache:
    """异步查询缓存类"""
    
    def __init__(self, max_size: int = None, ttl: int = None):
        self.max_size = max_size or PERFORMANCE_CONFIG['result_cache_size']
        self.ttl = ttl or PERFORMANCE_CONFIG['cache_ttl']
        self.cache: Dict[str, Tuple[Any, float]] = {}
        self._lock = asyncio.Lock()

    def _generate_key(self, sql_query: str) -> str:
        """生成缓存键"""
        return hashlib.md5(sql_query.encode()).hexdigest()

    async def get(self, sql_query: str) -> Optional[Any]:
        """获取缓存结果"""
        if not PERFORMANCE_CONFIG['enable_query_cache']:
            return None
            
        key = self._generate_key(sql_query)
        async with self._lock:
            if key in self.cache:
                result, timestamp = self.cache[key]
                # 检查是否过期
                if time.time() - timestamp < self.ttl:
                    return result
                else:
                    # 移除过期缓存
                    del self.cache[key]
        return None

    async def set(self, sql_query: str, result: Any) -> None:
        """设置缓存结果"""
        if not PERFORMANCE_CONFIG['enable_query_cache']:
            return
            
        key = self._generate_key(sql_query)
        async with self._lock:
            # 如果缓存已满，移除最旧的条目
            if len(self.cache) >= self.max_size:
                oldest_key = min(self.cache.keys(), key=lambda k: self.cache[k][1])
                del self.cache[oldest_key]
            
            self.cache[key] = (result, time.time())

    async def clear(self) -> None:
        """清空缓存"""
        async with self._lock:
            self.cache.clear()

    async def cleanup_expired(self) -> None:
        """清理过期缓存"""
        current_time = time.time()
        async with self._lock:
            expired_keys = [
                key for key, (_, timestamp) in self.cache.items()
                if current_time - timestamp >= self.ttl
            ]
            for key in expired_keys:
                del self.cache[key]


# 全局查询缓存实例
query_cache = AsyncQueryCache()
