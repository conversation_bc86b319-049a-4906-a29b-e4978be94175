from .utils.async_db_source import AsyncHITLSQLDatabase

class AsyncDataBaseEnv:
    """异步数据库环境类"""
    
    def __init__(self, database: AsyncHITLSQLDatabase):
        self.database = database
        self.dialect = database.dialect
        self.mschema = database.mschema
        self.db_name = database.db_name
        self._mschema_str = None

    @property 
    def mschema_str(self) -> str:
        """获取mschema字符串，懒加载"""
        if self._mschema_str is None:
            self._mschema_str = self.mschema.to_mschema()
        return self._mschema_str

    async def initialize(self):
        """异步初始化数据库环境"""
        await self.database.init_metadata()
        # 重新获取mschema字符串
        self._mschema_str = self.mschema.to_mschema()
