#!/usr/bin/env python3
"""
高并发基准测试 - 专门测试异步版本在高并发下的性能
"""

import asyncio
import time
import statistics
import sys
import os
import sqlite3
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from xiyan_mcp_server.utils.db_config import DBConfig
from xiyan_mcp_server.utils.db_util import init_db_conn, init_async_db_conn
from xiyan_mcp_server.utils.db_source import HITLSQLDatabase
from xiyan_mcp_server.utils.async_db_source import AsyncHITLSQLDatabase
from xiyan_mcp_server.database_env import DataBaseEnv
from xiyan_mcp_server.async_database_env import AsyncDataBaseEnv


class ConcurrencyBenchmark:
    def __init__(self, db_config: DBConfig):
        self.db_config = db_config
        self.queries = [
            "SELECT COUNT(*) FROM test_table",
            "SELECT * FROM test_table WHERE value > 50 LIMIT 10",
            "SELECT AVG(value) FROM test_table",
            "SELECT MAX(id), MIN(id) FROM test_table",
            "SELECT name FROM test_table WHERE id % 10 = 0",
        ]

    def sync_worker(self, query_count: int) -> list:
        """同步工作线程"""
        results = []
        
        # 每个线程创建自己的连接
        db_engine = init_db_conn(self.db_config)
        db_source = HITLSQLDatabase(db_engine)
        db_env = DataBaseEnv(db_source)
        
        for i in range(query_count):
            query = self.queries[i % len(self.queries)]
            start_time = time.time()
            success, result = db_env.database.fetch(query)
            duration = time.time() - start_time
            results.append((success, duration))
        
        return results

    async def async_worker(self, query_count: int) -> list:
        """异步工作协程"""
        results = []
        
        # 创建异步连接
        async_engine = init_async_db_conn(self.db_config)
        async_db_source = AsyncHITLSQLDatabase(async_engine)
        async_db_env = AsyncDataBaseEnv(async_db_source)
        await async_db_env.initialize()
        
        for i in range(query_count):
            query = self.queries[i % len(self.queries)]
            start_time = time.time()
            success, result = await async_db_env.database.fetch(query)
            duration = time.time() - start_time
            results.append((success, duration))
        
        await async_engine.dispose()
        return results

    def run_sync_benchmark(self, num_workers: int, queries_per_worker: int) -> dict:
        """运行同步基准测试"""
        print(f"🔄 运行同步基准测试: {num_workers} 线程, 每线程 {queries_per_worker} 查询")
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=num_workers) as executor:
            futures = [executor.submit(self.sync_worker, queries_per_worker) for _ in range(num_workers)]
            all_results = []
            
            for future in as_completed(futures):
                worker_results = future.result()
                all_results.extend(worker_results)
        
        total_time = time.time() - start_time
        
        # 统计结果
        successful_queries = sum(1 for success, _ in all_results if success)
        failed_queries = len(all_results) - successful_queries
        query_times = [duration for success, duration in all_results if success]
        
        return {
            'total_time': total_time,
            'total_queries': len(all_results),
            'successful_queries': successful_queries,
            'failed_queries': failed_queries,
            'avg_query_time': statistics.mean(query_times) if query_times else 0,
            'min_query_time': min(query_times) if query_times else 0,
            'max_query_time': max(query_times) if query_times else 0,
            'queries_per_second': successful_queries / total_time if total_time > 0 else 0,
        }

    async def run_async_benchmark(self, num_workers: int, queries_per_worker: int) -> dict:
        """运行异步基准测试"""
        print(f"⚡ 运行异步基准测试: {num_workers} 协程, 每协程 {queries_per_worker} 查询")
        
        start_time = time.time()
        
        tasks = [self.async_worker(queries_per_worker) for _ in range(num_workers)]
        results_list = await asyncio.gather(*tasks)
        
        total_time = time.time() - start_time
        
        # 合并所有结果
        all_results = []
        for worker_results in results_list:
            all_results.extend(worker_results)
        
        # 统计结果
        successful_queries = sum(1 for success, _ in all_results if success)
        failed_queries = len(all_results) - successful_queries
        query_times = [duration for success, duration in all_results if success]
        
        return {
            'total_time': total_time,
            'total_queries': len(all_results),
            'successful_queries': successful_queries,
            'failed_queries': failed_queries,
            'avg_query_time': statistics.mean(query_times) if query_times else 0,
            'min_query_time': min(query_times) if query_times else 0,
            'max_query_time': max(query_times) if query_times else 0,
            'queries_per_second': successful_queries / total_time if total_time > 0 else 0,
        }

    def print_results(self, sync_results: dict, async_results: dict, scenario: str):
        """打印测试结果"""
        print(f"\n📊 {scenario} 测试结果")
        print("-" * 60)
        
        print(f"{'指标':<20} {'同步版本':<15} {'异步版本':<15} {'提升':<10}")
        print("-" * 60)
        
        # 总时间
        sync_time = sync_results['total_time']
        async_time = async_results['total_time']
        time_improvement = ((sync_time - async_time) / sync_time * 100) if sync_time > 0 else 0
        print(f"{'总时间(秒)':<20} {sync_time:<15.4f} {async_time:<15.4f} {time_improvement:<10.1f}%")
        
        # QPS
        sync_qps = sync_results['queries_per_second']
        async_qps = async_results['queries_per_second']
        qps_improvement = ((async_qps - sync_qps) / sync_qps * 100) if sync_qps > 0 else 0
        print(f"{'QPS':<20} {sync_qps:<15.1f} {async_qps:<15.1f} {qps_improvement:<10.1f}%")
        
        # 平均查询时间
        sync_avg = sync_results['avg_query_time']
        async_avg = async_results['avg_query_time']
        avg_improvement = ((sync_avg - async_avg) / sync_avg * 100) if sync_avg > 0 else 0
        print(f"{'平均查询时间(ms)':<20} {sync_avg*1000:<15.2f} {async_avg*1000:<15.2f} {avg_improvement:<10.1f}%")
        
        # 成功率
        sync_success_rate = (sync_results['successful_queries'] / sync_results['total_queries'] * 100) if sync_results['total_queries'] > 0 else 0
        async_success_rate = (async_results['successful_queries'] / async_results['total_queries'] * 100) if async_results['total_queries'] > 0 else 0
        print(f"{'成功率(%)':<20} {sync_success_rate:<15.1f} {async_success_rate:<15.1f} {async_success_rate-sync_success_rate:<10.1f}")

    async def run_full_benchmark(self):
        """运行完整的基准测试"""
        print("🚀 XiYan MCP 高并发基准测试")
        print("=" * 80)
        
        # 测试场景配置
        scenarios = [
            (10, 5, "轻度并发"),
            (20, 10, "中度并发"),
            (50, 10, "高度并发"),
            (100, 5, "极高并发"),
        ]
        
        summary_results = []
        
        for num_workers, queries_per_worker, scenario_name in scenarios:
            print(f"\n🎯 开始 {scenario_name} 测试...")
            print(f"并发数: {num_workers}, 每个工作单元查询数: {queries_per_worker}")
            print(f"总查询数: {num_workers * queries_per_worker}")
            
            # 同步测试
            sync_results = self.run_sync_benchmark(num_workers, queries_per_worker)
            
            # 异步测试
            async_results = await self.run_async_benchmark(num_workers, queries_per_worker)
            
            # 打印结果
            self.print_results(sync_results, async_results, scenario_name)
            
            # 保存摘要数据
            summary_results.append({
                'scenario': scenario_name,
                'workers': num_workers,
                'total_queries': num_workers * queries_per_worker,
                'sync_qps': sync_results['queries_per_second'],
                'async_qps': async_results['queries_per_second'],
                'qps_improvement': ((async_results['queries_per_second'] - sync_results['queries_per_second']) / sync_results['queries_per_second'] * 100) if sync_results['queries_per_second'] > 0 else 0,
                'time_improvement': ((sync_results['total_time'] - async_results['total_time']) / sync_results['total_time'] * 100) if sync_results['total_time'] > 0 else 0,
            })
        
        # 打印总结
        self.print_summary(summary_results)

    def print_summary(self, results: list):
        """打印测试总结"""
        print("\n📈 基准测试总结")
        print("=" * 80)
        
        print(f"{'场景':<15} {'并发数':<8} {'总查询':<8} {'同步QPS':<12} {'异步QPS':<12} {'QPS提升':<10} {'时间提升':<10}")
        print("-" * 80)
        
        for result in results:
            print(f"{result['scenario']:<15} {result['workers']:<8} {result['total_queries']:<8} "
                  f"{result['sync_qps']:<12.1f} {result['async_qps']:<12.1f} "
                  f"{result['qps_improvement']:<10.1f}% {result['time_improvement']:<10.1f}%")
        
        # 平均提升
        avg_qps_improvement = statistics.mean([r['qps_improvement'] for r in results])
        avg_time_improvement = statistics.mean([r['time_improvement'] for r in results])
        
        print("-" * 80)
        print(f"{'平均提升':<15} {'':<8} {'':<8} {'':<12} {'':<12} {avg_qps_improvement:<10.1f}% {avg_time_improvement:<10.1f}%")
        
        print(f"\n🎉 测试完成! 异步版本在高并发场景下平均性能提升:")
        print(f"   ⚡ QPS提升: {avg_qps_improvement:.1f}%")
        print(f"   🚀 时间效率提升: {avg_time_improvement:.1f}%")
        
        if avg_qps_improvement > 20:
            print("\n✅ 异步升级效果显著，建议在生产环境中使用异步版本!")
        elif avg_qps_improvement > 0:
            print("\n✅ 异步版本有一定性能提升，适合并发场景使用。")
        else:
            print("\n⚠️  在当前测试环境下异步优势不明显，可能受到数据库或硬件限制。")


async def main():
    """主函数"""
    # 创建测试数据库
    test_db_path = "/tmp/benchmark_test.db"
    
    # 创建测试数据
    conn = sqlite3.connect(test_db_path)
    cursor = conn.cursor()
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS test_table (
            id INTEGER PRIMARY KEY,
            name TEXT,
            value INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # 插入更多测试数据以模拟真实场景
    print("📝 准备测试数据...")
    for i in range(1000):
        cursor.execute("INSERT OR REPLACE INTO test_table (id, name, value) VALUES (?, ?, ?)", 
                      (i, f'benchmark_test_{i}', i * 5))
    conn.commit()
    conn.close()
    print("✅ 测试数据准备完成")
    
    db_config = DBConfig(
        dialect='sqlite',
        db_path=test_db_path
    )
    
    try:
        benchmark = ConcurrencyBenchmark(db_config)
        await benchmark.run_full_benchmark()
    except Exception as e:
        print(f"❌ 基准测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理测试数据库
        if os.path.exists(test_db_path):
            os.remove(test_db_path)
            print("\n🧹 测试数据已清理")


if __name__ == "__main__":
    print("开始高并发基准测试...")
    print("此测试将比较同步和异步版本在不同并发级别下的性能表现")
    print("=" * 80)
    
    asyncio.run(main())
