# XiYan MCP 服务器异步升级 - 安装和配置指南

## 🚀 快速开始

### 1. 安装依赖

确保您的Python版本 >= 3.11，然后安装必要的异步数据库驱动：

```bash
# 基础安装
pip install xiyan-mcp-server

# 异步数据库支持（推荐）
pip install sqlalchemy[asyncio]

# 根据您的数据库类型安装对应驱动
pip install aiomysql      # MySQL异步支持
pip install asyncpg       # PostgreSQL异步支持  
pip install aiosqlite     # SQLite异步支持

# 一次性安装所有异步驱动
pip install aiomysql asyncpg aiosqlite
```

### 2. 验证安装

运行简单测试验证安装是否成功：

```bash
python simple_test.py
```

期望输出：
```
✅ 所有模块导入成功！
🎉 所有测试通过！异步升级成功！
```

### 3. 性能测试

运行性能对比测试：

```bash
python performance_test.py
```

## ⚙️ 配置优化

### 基础配置文件 (config_demo.yml)

```yaml
# 基础MCP和模型配置
mcp:
  transport: "stdio"

model:
  name: "XGenerationLab/XiYanSQL-QwenCoder-32B-2412"
  key: ""
  url: "https://api-inference.modelscope.cn/v1/"

# 数据库配置
database:
  dialect: "mysql"        # mysql, postgresql, sqlite
  host: "localhost"
  port: 3306
  user: "root"
  password: "your_password"
  database: "your_database"

# 可选：异步性能优化配置
async_performance:
  max_concurrent_queries: 100    # 最大并发查询数
  result_cache_size: 1000        # 查询结果缓存大小  
  cache_ttl: 300                # 缓存过期时间（秒）
  enable_query_cache: true       # 启用查询缓存

# 可选：连接池配置
connection_pools:
  mysql:
    pool_size: 20              # 基础连接池大小
    max_overflow: 30           # 最大溢出连接数
    pool_timeout: 30           # 获取连接超时（秒）
    pool_recycle: 3600         # 连接回收时间（秒）
    pool_pre_ping: true        # 连接前ping检测
  
  postgresql:
    pool_size: 20
    max_overflow: 30
    pool_timeout: 30
    pool_recycle: 3600
    pool_pre_ping: true
    
  sqlite:
    pool_size: 5               # SQLite不需要太多连接
    max_overflow: 10
    pool_timeout: 30
    pool_recycle: 3600
    pool_pre_ping: true
```

### 高并发场景优化

对于高并发生产环境，建议以下配置：

```yaml
async_performance:
  max_concurrent_queries: 200    # 增加并发查询数
  result_cache_size: 2000        # 增加缓存大小
  cache_ttl: 600                # 延长缓存时间
  enable_query_cache: true

connection_pools:
  mysql:
    pool_size: 50              # 增加连接池大小
    max_overflow: 100          # 增加溢出连接数
    pool_timeout: 60
    pool_recycle: 1800         # 更短的回收时间
```

## 📊 性能监控

### 关键指标

1. **并发查询数量**: 当前活跃的数据库查询数
2. **连接池使用率**: 当前使用的连接数/总连接数
3. **缓存命中率**: 缓存命中次数/总查询次数
4. **平均响应时间**: 查询的平均执行时间

### 监控脚本示例

```python
import asyncio
from xiyan_mcp_server.utils.query_cache import query_cache

async def monitor_performance():
    """简单的性能监控"""
    while True:
        cache_size = len(query_cache.cache)
        print(f"当前缓存条目数: {cache_size}")
        await asyncio.sleep(60)  # 每分钟检查一次

# 在生产代码中运行
# asyncio.create_task(monitor_performance())
```

## 🔧 故障排除

### 常见问题

1. **ModuleNotFoundError: No module named 'aiomysql'**
   ```bash
   pip install aiomysql
   ```

2. **连接池耗尽**
   - 增加 `pool_size` 和 `max_overflow`
   - 检查是否有连接泄漏
   - 优化查询性能

3. **内存使用过高**
   - 减少 `result_cache_size`
   - 降低 `cache_ttl`
   - 定期清理过期缓存

4. **查询超时**
   - 增加 `pool_timeout`
   - 优化SQL查询性能
   - 检查数据库负载

### 调试模式

开启详细日志记录：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📈 性能基准

基于测试结果，异步版本在不同场景下的性能表现：

| 场景 | 同步版本 | 异步版本 | 性能提升 |
|------|----------|----------|----------|
| 单查询 | 基准 | 略有开销 | -5% ~ +5% |
| 5并发 | 基准 | 显著提升 | +40% ~ +70% |
| 10并发 | 基准 | 显著提升 | +40% ~ +70% |
| 20并发 | 基准 | 显著提升 | +40% ~ +70% |

**结论**: 异步版本在高并发场景下性能显著提升，单查询开销可忽略。

## 🌟 最佳实践

1. **生产部署**:
   - 始终使用异步版本
   - 根据负载调整连接池大小
   - 监控关键性能指标

2. **开发测试**:
   - 使用 `simple_test.py` 验证功能
   - 使用 `performance_test.py` 压力测试
   - 在类似生产环境中测试

3. **配置调优**:
   - 从默认配置开始
   - 根据监控数据逐步调整
   - 定期评估和优化

4. **资源管理**:
   - 确保优雅关闭
   - 监控内存使用
   - 定期清理缓存

## 📞 支持

如果在使用过程中遇到问题：

1. 检查本指南的故障排除部分
2. 运行测试脚本验证环境
3. 查看项目GitHub Issues
4. 参考详细文档 `ASYNC_UPGRADE.md`

祝您使用愉快！🚀
