[project]
name = "xiyan_mcp_server"
version = "0.1.5.dev0"
description = "A Model Context Protocol (MCP) server that utilizes XiyanSQL with databases. This server enables AI assistants to list tables, read data, and execute natural language queries"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "mcp>=1.0.0",
    "mysql-connector-python>=9.1.0",
    "llama_index",
    "sqlalchemy",
    "pymysql"
]
[[project.authors]]
name = "<PERSON><PERSON><PERSON>o"
email = "<EMAIL>"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project.scripts]
mysql_mcp_server = "xiyan_mcp_server:main"