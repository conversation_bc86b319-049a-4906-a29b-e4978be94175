#!/usr/bin/env python3
"""
简单的功能测试脚本 - 验证异步数据库连接
"""

import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from xiyan_mcp_server.utils.db_config import DBConfig
    from xiyan_mcp_server.utils.db_util import init_async_db_conn
    from xiyan_mcp_server.utils.async_db_source import AsyncHITLSQLDatabase
    from xiyan_mcp_server.async_database_env import AsyncDataBaseEnv
    print("✅ 所有模块导入成功！")
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    sys.exit(1)


async def test_async_connection():
    """测试异步数据库连接"""
    print("\n🔧 测试异步数据库连接...")
    
    # 使用SQLite进行测试（不需要外部数据库）
    try:
        # 创建一个测试SQLite数据库
        import sqlite3
        test_db_path = "/tmp/test_async.db"
        
        # 创建测试数据库和表
        conn = sqlite3.connect(test_db_path)
        cursor = conn.cursor()
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS test_table (
                id INTEGER PRIMARY KEY,
                name TEXT,
                value INTEGER
            )
        """)
        cursor.execute("INSERT OR REPLACE INTO test_table (id, name, value) VALUES (1, 'test', 100)")
        cursor.execute("INSERT OR REPLACE INTO test_table (id, name, value) VALUES (2, 'async', 200)")
        conn.commit()
        conn.close()
        
        # 测试异步连接
        db_config = DBConfig(
            dialect='sqlite',
            db_path=test_db_path
        )
        
        print("📡 创建异步数据库引擎...")
        async_engine = init_async_db_conn(db_config)
        
        print("🏗️  创建异步数据库源...")
        async_db_source = AsyncHITLSQLDatabase(async_engine, db_name="test_db")
        
        print("🌐 创建异步数据库环境...")
        async_db_env = AsyncDataBaseEnv(async_db_source)
        
        print("⚡ 初始化异步环境...")
        await async_db_env.initialize()
        
        print("🔍 执行测试查询...")
        success, result = await async_db_env.database.fetch("SELECT * FROM test_table")
        
        if success:
            print(f"✅ 查询成功! 结果: {result}")
        else:
            print(f"❌ 查询失败: {result}")
        
        print("📊 获取MSchema...")
        mschema_str = async_db_env.mschema_str
        print(f"✅ MSchema生成成功，长度: {len(mschema_str)} 字符")
        
        print("🧹 清理资源...")
        await async_engine.dispose()
        
        # 删除测试数据库
        os.remove(test_db_path)
        
        print("🎉 异步数据库连接测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_query_cache():
    """测试查询缓存功能"""
    print("\n💾 测试查询缓存...")
    
    try:
        from xiyan_mcp_server.utils.query_cache import AsyncQueryCache
        
        cache = AsyncQueryCache(max_size=5, ttl=10)
        
        # 测试设置和获取缓存
        test_query = "SELECT 1"
        test_result = (True, [(1,)])
        
        print("📝 设置缓存...")
        await cache.set(test_query, test_result)
        
        print("🔍 获取缓存...")
        cached_result = await cache.get(test_query)
        
        if cached_result == test_result:
            print("✅ 缓存功能正常工作！")
            return True
        else:
            print(f"❌ 缓存结果不匹配: {cached_result} != {test_result}")
            return False
            
    except Exception as e:
        print(f"❌ 缓存测试失败: {str(e)}")
        return False


async def main():
    """主测试函数"""
    print("🚀 XiYan MCP 异步功能测试")
    print("=" * 50)
    
    # 测试模块导入
    print("✅ 模块导入测试通过")
    
    # 测试异步数据库连接
    db_test_success = await test_async_connection()
    
    # 测试查询缓存
    cache_test_success = await test_query_cache()
    
    # 总结
    print("\n📋 测试总结")
    print("=" * 50)
    print(f"数据库连接测试: {'✅ 通过' if db_test_success else '❌ 失败'}")
    print(f"查询缓存测试: {'✅ 通过' if cache_test_success else '❌ 失败'}")
    
    if db_test_success and cache_test_success:
        print("\n🎉 所有测试通过！异步升级成功！")
        print("\n💡 建议:")
        print("- 可以开始使用异步版本获得更好的并发性能")
        print("- 根据实际负载调整连接池和缓存配置")
        print("- 在生产环境中监控性能指标")
    else:
        print("\n⚠️  部分测试失败，请检查配置和依赖")


if __name__ == "__main__":
    asyncio.run(main())
